import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';

// 全局错误处理 - 忽略以太坊钱包冲突错误
window.addEventListener('error', (event) => {
  const message = event.error?.message || event.message || '';
  if (
    message.includes('ethereum') ||
    message.includes('MetaMask') ||
    message.includes('Cannot set property ethereum') ||
    message.includes('Cannot redefine property: ethereum')
  ) {
    event.preventDefault();
    event.stopPropagation();
    return false;
  }
});

window.addEventListener('unhandledrejection', (event) => {
  const message = event.reason?.message || event.reason || '';
  if (
    message.includes('ethereum') ||
    message.includes('MetaMask') ||
    message.includes('Cannot set property ethereum') ||
    message.includes('Cannot redefine property: ethereum')
  ) {
    event.preventDefault();
    return false;
  }
});

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
