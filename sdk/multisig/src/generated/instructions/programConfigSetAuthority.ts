/**
 * This code was GENERATED using the solita package.
 * Please DO NOT EDIT THIS FILE, instead rerun solita to update it or write a wrapper to add functionality.
 *
 * See: https://github.com/metaplex-foundation/solita
 */

import * as beet from '@metaplex-foundation/beet'
import * as web3 from '@solana/web3.js'
import {
  ProgramConfigSetAuthorityArgs,
  programConfigSetAuthorityArgsBeet,
} from '../types/ProgramConfigSetAuthorityArgs'

/**
 * @category Instructions
 * @category ProgramConfigSetAuthority
 * @category generated
 */
export type ProgramConfigSetAuthorityInstructionArgs = {
  args: ProgramConfigSetAuthorityArgs
}
/**
 * @category Instructions
 * @category ProgramConfigSetAuthority
 * @category generated
 */
export const programConfigSetAuthorityStruct = new beet.BeetArgsStruct<
  ProgramConfigSetAuthorityInstructionArgs & {
    instructionDiscriminator: number[] /* size: 8 */
  }
>(
  [
    ['instructionDiscriminator', beet.uniformFixedSizeArray(beet.u8, 8)],
    ['args', programConfigSetAuthorityArgsBeet],
  ],
  'ProgramConfigSetAuthorityInstructionArgs'
)
/**
 * Accounts required by the _programConfigSetAuthority_ instruction
 *
 * @property [_writable_] programConfig
 * @property [**signer**] authority
 * @category Instructions
 * @category ProgramConfigSetAuthority
 * @category generated
 */
export type ProgramConfigSetAuthorityInstructionAccounts = {
  programConfig: web3.PublicKey
  authority: web3.PublicKey
  anchorRemainingAccounts?: web3.AccountMeta[]
}

export const programConfigSetAuthorityInstructionDiscriminator = [
  238, 242, 36, 181, 32, 143, 216, 75,
]

/**
 * Creates a _ProgramConfigSetAuthority_ instruction.
 *
 * @param accounts that will be accessed while the instruction is processed
 * @param args to provide as instruction data to the program
 *
 * @category Instructions
 * @category ProgramConfigSetAuthority
 * @category generated
 */
export function createProgramConfigSetAuthorityInstruction(
  accounts: ProgramConfigSetAuthorityInstructionAccounts,
  args: ProgramConfigSetAuthorityInstructionArgs,
  programId = new web3.PublicKey('SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf')
) {
  const [data] = programConfigSetAuthorityStruct.serialize({
    instructionDiscriminator: programConfigSetAuthorityInstructionDiscriminator,
    ...args,
  })
  const keys: web3.AccountMeta[] = [
    {
      pubkey: accounts.programConfig,
      isWritable: true,
      isSigner: false,
    },
    {
      pubkey: accounts.authority,
      isWritable: false,
      isSigner: true,
    },
  ]

  if (accounts.anchorRemainingAccounts != null) {
    for (const acc of accounts.anchorRemainingAccounts) {
      keys.push(acc)
    }
  }

  const ix = new web3.TransactionInstruction({
    programId,
    keys,
    data,
  })
  return ix
}
