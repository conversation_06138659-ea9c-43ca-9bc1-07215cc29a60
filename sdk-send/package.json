{"name": "@sqds/multisig-send", "version": "1.0.0", "description": "Squads Multisig SOL Transfer - 实现 Squads 平台的 SOL 转账功能", "main": "index.js", "type": "module", "license": "MIT", "scripts": {"example": "node examples/basic-transfer.js", "example:create": "node examples/create-multisig.js", "example:config": "node examples/generate-multisig-config.js", "test": "node test-import.js"}, "dependencies": {"@solana/spl-token": "^0.4.13", "@solana/web3.js": "^1.70.3", "@sqds/multisig": "^2.1.4"}, "engines": {"node": ">=16"}, "keywords": ["solana", "multisig", "squads", "transfer", "sol", "blockchain", "crypto"], "author": "Squads Protocol", "repository": {"type": "git", "url": "https://github.com/Squads-Protocol/v4.git", "directory": "sdk/multisig-send"}}