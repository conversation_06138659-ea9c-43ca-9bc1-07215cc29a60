# Squads Multisig 前端 Demo 使用指南

## 🎯 项目概述

这是一个基于 Squads Protocol v4 的多签钱包前端演示应用，实现了完整的多签钱包功能，包括：

- 🔗 Phantom 钱包连接
- 💰 SOL 主币转账
- 🪙 Token 转账（USDC、USDT、自定义 Token）
- 📋 交易创建、投票、执行
- 👥 多签成员管理
- ⚙️ 多签配置管理

## 🚀 快速启动

### 方法一：使用启动脚本（推荐）

```bash
cd fe
./start-demo.sh
```

### 方法二：手动启动

```bash
cd fe
npm install
npm start
```

应用将在 `http://localhost:3000` 启动。

## 📱 功能演示

### 1. 钱包连接
- 点击"连接钱包"按钮
- 选择 Phantom 钱包
- 授权连接

### 2. 多签配置
- 切换到"多签配置"标签
- 设置多签地址（默认提供测试地址）
- 设置程序 ID（默认为 Squads 官方程序）
- 配置会自动保存到本地

### 3. SOL 转账演示
- 切换到"SOL 转账"标签
- 输入收款地址：`2hvBjn1R3nSXHJop7dz2xTTWSLkQiHSjPmpyjdQY7p7Y`
- 输入转账金额：`0.001`
- 点击"创建 SOL 转账"
- 系统会自动创建交易并投票

### 4. Token 转账演示
- 切换到"Token 转账"标签
- 选择 Token 类型（USDC/USDT）或自定义
- 输入收款地址和金额
- 点击创建转账

### 5. 交易管理
- 切换到"交易列表"标签
- 查看所有交易状态
- 对交易进行投票批准
- 执行达到阈值的交易

### 6. 成员管理
- 切换到"成员管理"标签
- 查看当前多签成员
- 添加新成员
- 移除现有成员

## 🔧 技术实现

### 核心依赖
- `@solana/wallet-adapter-react`: Solana 钱包适配器
- `@sqds/multisig`: Squads 多签 SDK
- `@solana/web3.js`: Solana Web3 库
- `@solana/spl-token`: SPL Token 库

### 关键组件
- `WalletProvider`: 钱包连接提供者
- `useMultisig`: 多签配置 Hook
- `TransferSol/TransferToken`: 转账组件
- `TransactionList`: 交易管理组件
- `MemberManagement`: 成员管理组件

### 交易流程
1. **创建交易**: 使用 `vaultTransactionCreate` 创建多签交易
2. **创建提案**: 使用 `proposalCreate` 创建投票提案
3. **投票批准**: 使用 `proposalApprove` 进行投票
4. **执行交易**: 使用 `vaultTransactionExecute` 执行交易

## 📋 测试场景

### 场景一：SOL 转账
1. 连接钱包
2. 配置多签地址
3. 创建 0.001 SOL 转账
4. 查看交易列表中的新交易
5. 如需要，进行额外投票
6. 执行交易

### 场景二：Token 转账
1. 选择 USDC Token
2. 输入转账金额 0.01
3. 创建转账交易
4. 管理交易投票和执行

### 场景三：成员管理
1. 查看当前成员列表
2. 添加新成员地址
3. 创建成员变更交易
4. 投票并执行成员变更

## 🛠️ 开发说明

### 项目结构
```
fe/
├── src/
│   ├── components/     # React 组件
│   ├── hooks/         # 自定义 Hooks
│   ├── App.js         # 主应用
│   └── index.js       # 入口文件
├── public/            # 静态资源
├── package.json       # 项目配置
└── webpack.config.js  # 构建配置
```

### 自定义配置
- 修改 `WalletProvider.js` 中的网络配置
- 调整 `useMultisig.js` 中的默认配置
- 在各组件中添加新功能

### 扩展功能
- 添加更多 Token 支持
- 实现批量转账
- 添加交易历史记录
- 集成更多钱包类型

## ⚠️ 注意事项

1. **测试环境**: 当前使用 Devnet，请勿用于主网
2. **钱包余额**: 确保有足够 SOL 支付交易费用
3. **多签配置**: 使用正确的多签地址和程序 ID
4. **网络延迟**: 交易确认需要时间，请耐心等待
5. **错误处理**: 遇到问题请查看浏览器控制台

## 🔗 参考资源

- [Squads Protocol 文档](https://docs.squads.so/)
- [Solana Web3.js 文档](https://solana-labs.github.io/solana-web3.js/)
- [Phantom 钱包文档](https://docs.phantom.app/)

## 📞 技术支持

如遇到问题，请：
1. 检查浏览器控制台错误信息
2. 确认网络连接和钱包状态
3. 验证多签配置是否正确
4. 查看交易状态和余额情况

---

**祝您使用愉快！** 🎉
