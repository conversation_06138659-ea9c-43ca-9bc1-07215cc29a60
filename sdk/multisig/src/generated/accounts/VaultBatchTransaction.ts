/**
 * This code was GENERATED using the solita package.
 * Please DO NOT EDIT THIS FILE, instead rerun solita to update it or write a wrapper to add functionality.
 *
 * See: https://github.com/metaplex-foundation/solita
 */

import * as beet from '@metaplex-foundation/beet'
import * as web3 from '@solana/web3.js'
import * as beetSolana from '@metaplex-foundation/beet-solana'
import {
  VaultTransactionMessage,
  vaultTransactionMessageBeet,
} from '../types/VaultTransactionMessage'

/**
 * Arguments used to create {@link VaultBatchTransaction}
 * @category Accounts
 * @category generated
 */
export type VaultBatchTransactionArgs = {
  bump: number
  ephemeralSignerBumps: Uint8Array
  message: VaultTransactionMessage
}

export const vaultBatchTransactionDiscriminator = [
  196, 121, 46, 36, 12, 19, 252, 7,
]
/**
 * Holds the data for the {@link VaultBatchTransaction} Account and provides de/serialization
 * functionality for that data
 *
 * @category Accounts
 * @category generated
 */
export class VaultBatchTransaction implements VaultBatchTransactionArgs {
  private constructor(
    readonly bump: number,
    readonly ephemeralSignerBumps: Uint8Array,
    readonly message: VaultTransactionMessage
  ) {}

  /**
   * Creates a {@link VaultBatchTransaction} instance from the provided args.
   */
  static fromArgs(args: VaultBatchTransactionArgs) {
    return new VaultBatchTransaction(
      args.bump,
      args.ephemeralSignerBumps,
      args.message
    )
  }

  /**
   * Deserializes the {@link VaultBatchTransaction} from the data of the provided {@link web3.AccountInfo}.
   * @returns a tuple of the account data and the offset up to which the buffer was read to obtain it.
   */
  static fromAccountInfo(
    accountInfo: web3.AccountInfo<Buffer>,
    offset = 0
  ): [VaultBatchTransaction, number] {
    return VaultBatchTransaction.deserialize(accountInfo.data, offset)
  }

  /**
   * Retrieves the account info from the provided address and deserializes
   * the {@link VaultBatchTransaction} from its data.
   *
   * @throws Error if no account info is found at the address or if deserialization fails
   */
  static async fromAccountAddress(
    connection: web3.Connection,
    address: web3.PublicKey,
    commitmentOrConfig?: web3.Commitment | web3.GetAccountInfoConfig
  ): Promise<VaultBatchTransaction> {
    const accountInfo = await connection.getAccountInfo(
      address,
      commitmentOrConfig
    )
    if (accountInfo == null) {
      throw new Error(
        `Unable to find VaultBatchTransaction account at ${address}`
      )
    }
    return VaultBatchTransaction.fromAccountInfo(accountInfo, 0)[0]
  }

  /**
   * Provides a {@link web3.Connection.getProgramAccounts} config builder,
   * to fetch accounts matching filters that can be specified via that builder.
   *
   * @param programId - the program that owns the accounts we are filtering
   */
  static gpaBuilder(
    programId: web3.PublicKey = new web3.PublicKey(
      'SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf'
    )
  ) {
    return beetSolana.GpaBuilder.fromStruct(
      programId,
      vaultBatchTransactionBeet
    )
  }

  /**
   * Deserializes the {@link VaultBatchTransaction} from the provided data Buffer.
   * @returns a tuple of the account data and the offset up to which the buffer was read to obtain it.
   */
  static deserialize(buf: Buffer, offset = 0): [VaultBatchTransaction, number] {
    return vaultBatchTransactionBeet.deserialize(buf, offset)
  }

  /**
   * Serializes the {@link VaultBatchTransaction} into a Buffer.
   * @returns a tuple of the created Buffer and the offset up to which the buffer was written to store it.
   */
  serialize(): [Buffer, number] {
    return vaultBatchTransactionBeet.serialize({
      accountDiscriminator: vaultBatchTransactionDiscriminator,
      ...this,
    })
  }

  /**
   * Returns the byteSize of a {@link Buffer} holding the serialized data of
   * {@link VaultBatchTransaction} for the provided args.
   *
   * @param args need to be provided since the byte size for this account
   * depends on them
   */
  static byteSize(args: VaultBatchTransactionArgs) {
    const instance = VaultBatchTransaction.fromArgs(args)
    return vaultBatchTransactionBeet.toFixedFromValue({
      accountDiscriminator: vaultBatchTransactionDiscriminator,
      ...instance,
    }).byteSize
  }

  /**
   * Fetches the minimum balance needed to exempt an account holding
   * {@link VaultBatchTransaction} data from rent
   *
   * @param args need to be provided since the byte size for this account
   * depends on them
   * @param connection used to retrieve the rent exemption information
   */
  static async getMinimumBalanceForRentExemption(
    args: VaultBatchTransactionArgs,
    connection: web3.Connection,
    commitment?: web3.Commitment
  ): Promise<number> {
    return connection.getMinimumBalanceForRentExemption(
      VaultBatchTransaction.byteSize(args),
      commitment
    )
  }

  /**
   * Returns a readable version of {@link VaultBatchTransaction} properties
   * and can be used to convert to JSON and/or logging
   */
  pretty() {
    return {
      bump: this.bump,
      ephemeralSignerBumps: this.ephemeralSignerBumps,
      message: this.message,
    }
  }
}

/**
 * @category Accounts
 * @category generated
 */
export const vaultBatchTransactionBeet = new beet.FixableBeetStruct<
  VaultBatchTransaction,
  VaultBatchTransactionArgs & {
    accountDiscriminator: number[] /* size: 8 */
  }
>(
  [
    ['accountDiscriminator', beet.uniformFixedSizeArray(beet.u8, 8)],
    ['bump', beet.u8],
    ['ephemeralSignerBumps', beet.bytes],
    ['message', vaultTransactionMessageBeet],
  ],
  VaultBatchTransaction.fromArgs,
  'VaultBatchTransaction'
)
