/**
 * This code was GENERATED using the solita package.
 * Please DO NOT EDIT THIS FILE, instead rerun solita to update it or write a wrapper to add functionality.
 *
 * See: https://github.com/metaplex-foundation/solita
 */

import * as beet from '@metaplex-foundation/beet'
import { ConfigAction, configActionBeet } from './ConfigAction'
export type ConfigTransactionCreateArgs = {
  actions: ConfigAction[]
  memo: beet.COption<string>
}

/**
 * @category userTypes
 * @category generated
 */
export const configTransactionCreateArgsBeet =
  new beet.FixableBeetArgsStruct<ConfigTransactionCreateArgs>(
    [
      ['actions', beet.array(configActionBeet)],
      ['memo', beet.coption(beet.utf8String)],
    ],
    'ConfigTransactionCreateArgs'
  )
