/**
 * This code was GENERATED using the solita package.
 * Please DO NOT EDIT THIS FILE, instead rerun solita to update it or write a wrapper to add functionality.
 *
 * See: https://github.com/metaplex-foundation/solita
 */

import * as beet from '@metaplex-foundation/beet'
export type SpendingLimitUseArgs = {
  amount: beet.bignum
  decimals: number
  memo: beet.COption<string>
}

/**
 * @category userTypes
 * @category generated
 */
export const spendingLimitUseArgsBeet =
  new beet.FixableBeetArgsStruct<SpendingLimitUseArgs>(
    [
      ['amount', beet.u64],
      ['decimals', beet.u8],
      ['memo', beet.coption(beet.utf8String)],
    ],
    'SpendingLimitUseArgs'
  )
