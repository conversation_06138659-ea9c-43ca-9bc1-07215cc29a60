// 创建多签地址示例
// 使用: node examples/create-multisig-simple.js

import { Connection, Keypair, PublicKey } from '@solana/web3.js';
import * as multisig from '@sqds/multisig';

const connection = new Connection("https://api.mainnet-beta.solana.com", "confirmed");
const programId = new PublicKey("SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf");

async function main() {
  try {
    console.log('🚀 创建多签钱包');

    // 生成成员密钥对
    const member1 = Keypair.generate();
    const member2 = Keypair.generate();
    const member3 = Keypair.generate();
    const creator = member1; // 使用成员1作为创建者

    console.log('👥 成员信息:');
    console.log(`   成员1: ${member1.publicKey.toBase58()}`);
    console.log(`   成员2: ${member2.publicKey.toBase58()}`);
    console.log(`   成员3: ${member3.publicKey.toBase58()}`);

    // 多签配置
    const threshold = 2; // 2/3 多签
    const members = [
      { key: member1.publicKey, permissions: multisig.types.Permissions.all() },
      { key: member2.publicKey, permissions: multisig.types.Permissions.all() },
      { key: member3.publicKey, permissions: multisig.types.Permissions.all() },
    ];

    console.log(`📋 多签配置: ${threshold}/${members.length} 阈值`);

    // 生成多签地址
    const multisigKeypair = Keypair.generate();
    const multisigPda = multisigKeypair.publicKey;

    console.log(`🏦 多签地址: ${multisigPda.toBase58()}`);

    // 创建多签钱包
    const signature = await multisig.rpc.multisigCreate({
      connection,
      creator,
      multisigPda,
      configAuthority: null,
      timeLock: 0,
      members,
      threshold,
      rentCollector: null,
      programId,
    });

    console.log(`✅ 多签创建交易: ${signature}`);

    // 等待确认
    await connection.confirmTransaction(signature);
    console.log('✅ 交易已确认');

    // 获取金库地址
    const [vaultPda] = multisig.getVaultPda({
      multisigPda,
      index: 0,
      programId,
    });

    console.log(`💰 金库地址: ${vaultPda.toBase58()}`);

    // 显示私钥（仅用于测试）
    console.log('\n🔑 私钥信息（请安全保存）:');
    console.log(`成员1私钥: ${Buffer.from(member1.secretKey).toString('hex')}`);
    console.log(`成员2私钥: ${Buffer.from(member2.secretKey).toString('hex')}`);
    console.log(`成员3私钥: ${Buffer.from(member3.secretKey).toString('hex')}`);

    console.log('\n🎉 多签钱包创建完成');

  } catch (error) {
    console.error('❌ 创建失败:', error.message);
  }
}

main();
