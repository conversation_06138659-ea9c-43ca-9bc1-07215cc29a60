<!DOCTYPE html><html class="default" lang="en"><head><meta charSet="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>generated | @sqds/multisig</title><meta name="description" content="Documentation for @sqds/multisig"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script defer src="../assets/main.js"></script><script async src="../assets/search.js" id="tsd-search-script"></script><script async src="../assets/navigation.js" id="tsd-nav-script"></script></head><body><script>document.documentElement.dataset.theme = localStorage.getItem("tsd-theme") || "os"</script><header class="tsd-page-toolbar"><div class="tsd-toolbar-contents container"><div class="table-cell" id="tsd-search" data-base=".."><div class="field"><label for="tsd-search-field" class="tsd-widget tsd-toolbar-icon search no-caption"><svg width="16" height="16" viewBox="0 0 16 16" fill="none"><use href="#icon-search"></use></svg></label><input type="text" id="tsd-search-field" aria-label="Search"/></div><div class="field"><div id="tsd-toolbar-links"></div></div><ul class="results"><li class="state loading">Preparing search index...</li><li class="state failure">The search index is not available</li></ul><a href="../index.html" class="title">@sqds/multisig</a></div><div class="table-cell" id="tsd-widgets"><a href="#" class="tsd-widget tsd-toolbar-icon menu no-caption" data-toggle="menu" aria-label="Menu"><svg width="16" height="16" viewBox="0 0 16 16" fill="none"><use href="#icon-menu"></use></svg></a></div></div></header><div class="container container-main"><div class="col-content"><div class="tsd-page-title"><ul class="tsd-breadcrumb"><li><a href="../modules.html">@sqds/multisig</a></li><li><a href="generated.html">generated</a></li></ul><h1>Namespace generated</h1></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/Squads-Protocol/v4/blob/f59db7a/sdk/multisig/src/generated/index.ts#L1">sdk/multisig/src/generated/index.ts:1</a></li></ul></aside><section class="tsd-panel-group tsd-index-group"><section class="tsd-panel tsd-index-panel"><h3 class="tsd-index-heading uppercase">Index</h3><section class="tsd-index-section"><h3 class="tsd-index-heading">Accounts</h3><div class="tsd-index-list"><a href="../classes/generated.Batch.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Batch</span></a>
<a href="../classes/generated.ConfigTransaction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Config<wbr/>Transaction</span></a>
<a href="../classes/generated.Multisig.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Multisig</span></a>
<a href="../classes/generated.ProgramConfig.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Program<wbr/>Config</span></a>
<a href="../classes/generated.Proposal.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Proposal</span></a>
<a href="../classes/generated.SpendingLimit.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Spending<wbr/>Limit</span></a>
<a href="../classes/generated.VaultBatchTransaction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Vault<wbr/>Batch<wbr/>Transaction</span></a>
<a href="../classes/generated.VaultTransaction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Vault<wbr/>Transaction</span></a>
<a href="../types/generated.BatchArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Batch<wbr/>Args</span></a>
<a href="../types/generated.ConfigTransactionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Config<wbr/>Transaction<wbr/>Args</span></a>
<a href="../types/generated.MultisigArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Args</span></a>
<a href="../types/generated.ProgramConfigArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Program<wbr/>Config<wbr/>Args</span></a>
<a href="../types/generated.ProposalArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Proposal<wbr/>Args</span></a>
<a href="../types/generated.SpendingLimitArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Spending<wbr/>Limit<wbr/>Args</span></a>
<a href="../types/generated.VaultBatchTransactionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Vault<wbr/>Batch<wbr/>Transaction<wbr/>Args</span></a>
<a href="../types/generated.VaultTransactionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Vault<wbr/>Transaction<wbr/>Args</span></a>
<a href="../variables/generated.batchBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>batch<wbr/>Beet</span></a>
<a href="../variables/generated.configTransactionBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>config<wbr/>Transaction<wbr/>Beet</span></a>
<a href="../variables/generated.multisigBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Beet</span></a>
<a href="../variables/generated.programConfigBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>program<wbr/>Config<wbr/>Beet</span></a>
<a href="../variables/generated.proposalBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>proposal<wbr/>Beet</span></a>
<a href="../variables/generated.spendingLimitBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>spending<wbr/>Limit<wbr/>Beet</span></a>
<a href="../variables/generated.vaultBatchTransactionBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>vault<wbr/>Batch<wbr/>Transaction<wbr/>Beet</span></a>
<a href="../variables/generated.vaultTransactionBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>vault<wbr/>Transaction<wbr/>Beet</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">BatchAccountsClose</h3><div class="tsd-index-list"><a href="../types/generated.BatchAccountsCloseInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Batch<wbr/>Accounts<wbr/>Close<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../variables/generated.batchAccountsCloseStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>batch<wbr/>Accounts<wbr/>Close<wbr/>Struct</span></a>
<a href="../functions/generated.createBatchAccountsCloseInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Batch<wbr/>Accounts<wbr/>Close<wbr/>Instruction</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">BatchAddTransaction</h3><div class="tsd-index-list"><a href="../types/generated.BatchAddTransactionInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Batch<wbr/>Add<wbr/>Transaction<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.BatchAddTransactionInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Batch<wbr/>Add<wbr/>Transaction<wbr/>Instruction<wbr/>Args</span></a>
<a href="../variables/generated.batchAddTransactionStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>batch<wbr/>Add<wbr/>Transaction<wbr/>Struct</span></a>
<a href="../functions/generated.createBatchAddTransactionInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Batch<wbr/>Add<wbr/>Transaction<wbr/>Instruction</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">BatchCreate</h3><div class="tsd-index-list"><a href="../types/generated.BatchCreateInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Batch<wbr/>Create<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.BatchCreateInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Batch<wbr/>Create<wbr/>Instruction<wbr/>Args</span></a>
<a href="../variables/generated.batchCreateStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>batch<wbr/>Create<wbr/>Struct</span></a>
<a href="../functions/generated.createBatchCreateInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Batch<wbr/>Create<wbr/>Instruction</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">BatchExecuteTransaction</h3><div class="tsd-index-list"><a href="../types/generated.BatchExecuteTransactionInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Batch<wbr/>Execute<wbr/>Transaction<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../variables/generated.batchExecuteTransactionStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>batch<wbr/>Execute<wbr/>Transaction<wbr/>Struct</span></a>
<a href="../functions/generated.createBatchExecuteTransactionInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Batch<wbr/>Execute<wbr/>Transaction<wbr/>Instruction</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">ConfigTransactionAccountsClose</h3><div class="tsd-index-list"><a href="../types/generated.ConfigTransactionAccountsCloseInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Config<wbr/>Transaction<wbr/>Accounts<wbr/>Close<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../variables/generated.configTransactionAccountsCloseStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>config<wbr/>Transaction<wbr/>Accounts<wbr/>Close<wbr/>Struct</span></a>
<a href="../functions/generated.createConfigTransactionAccountsCloseInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Config<wbr/>Transaction<wbr/>Accounts<wbr/>Close<wbr/>Instruction</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">ConfigTransactionCreate</h3><div class="tsd-index-list"><a href="../types/generated.ConfigTransactionCreateInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Config<wbr/>Transaction<wbr/>Create<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.ConfigTransactionCreateInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Config<wbr/>Transaction<wbr/>Create<wbr/>Instruction<wbr/>Args</span></a>
<a href="../variables/generated.configTransactionCreateStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>config<wbr/>Transaction<wbr/>Create<wbr/>Struct</span></a>
<a href="../functions/generated.createConfigTransactionCreateInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Config<wbr/>Transaction<wbr/>Create<wbr/>Instruction</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">ConfigTransactionExecute</h3><div class="tsd-index-list"><a href="../types/generated.ConfigTransactionExecuteInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Config<wbr/>Transaction<wbr/>Execute<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../variables/generated.configTransactionExecuteStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>config<wbr/>Transaction<wbr/>Execute<wbr/>Struct</span></a>
<a href="../functions/generated.createConfigTransactionExecuteInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Config<wbr/>Transaction<wbr/>Execute<wbr/>Instruction</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">Errors</h3><div class="tsd-index-list"><a href="../classes/generated.AlreadyApprovedError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Already<wbr/>Approved<wbr/>Error</span></a>
<a href="../classes/generated.AlreadyCancelledError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Already<wbr/>Cancelled<wbr/>Error</span></a>
<a href="../classes/generated.AlreadyRejectedError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Already<wbr/>Rejected<wbr/>Error</span></a>
<a href="../classes/generated.BatchNotEmptyError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Batch<wbr/>Not<wbr/>Empty<wbr/>Error</span></a>
<a href="../classes/generated.DecimalsMismatchError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Decimals<wbr/>Mismatch<wbr/>Error</span></a>
<a href="../classes/generated.DuplicateMemberError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Duplicate<wbr/>Member<wbr/>Error</span></a>
<a href="../classes/generated.EmptyMembersError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Empty<wbr/>Members<wbr/>Error</span></a>
<a href="../classes/generated.IllegalAccountOwnerError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Illegal<wbr/>Account<wbr/>Owner<wbr/>Error</span></a>
<a href="../classes/generated.InvalidAccountError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Invalid<wbr/>Account<wbr/>Error</span></a>
<a href="../classes/generated.InvalidDestinationError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Invalid<wbr/>Destination<wbr/>Error</span></a>
<a href="../classes/generated.InvalidMintError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Invalid<wbr/>Mint<wbr/>Error</span></a>
<a href="../classes/generated.InvalidNumberOfAccountsError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Invalid<wbr/>Number<wbr/>Of<wbr/>Accounts<wbr/>Error</span></a>
<a href="../classes/generated.InvalidProposalStatusError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Invalid<wbr/>Proposal<wbr/>Status<wbr/>Error</span></a>
<a href="../classes/generated.InvalidRentCollectorError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Invalid<wbr/>Rent<wbr/>Collector<wbr/>Error</span></a>
<a href="../classes/generated.InvalidStaleTransactionIndexError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Invalid<wbr/>Stale<wbr/>Transaction<wbr/>Index<wbr/>Error</span></a>
<a href="../classes/generated.InvalidThresholdError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Invalid<wbr/>Threshold<wbr/>Error</span></a>
<a href="../classes/generated.InvalidTransactionIndexError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Invalid<wbr/>Transaction<wbr/>Index<wbr/>Error</span></a>
<a href="../classes/generated.InvalidTransactionMessageError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Invalid<wbr/>Transaction<wbr/>Message<wbr/>Error</span></a>
<a href="../classes/generated.MissingAccountError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Missing<wbr/>Account<wbr/>Error</span></a>
<a href="../classes/generated.NoActionsError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>No<wbr/>Actions<wbr/>Error</span></a>
<a href="../classes/generated.NoExecutorsError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>No<wbr/>Executors<wbr/>Error</span></a>
<a href="../classes/generated.NoProposersError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>No<wbr/>Proposers<wbr/>Error</span></a>
<a href="../classes/generated.NoVotersError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>No<wbr/>Voters<wbr/>Error</span></a>
<a href="../classes/generated.NotAMemberError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>NotAMember<wbr/>Error</span></a>
<a href="../classes/generated.NotSupportedForControlledError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Not<wbr/>Supported<wbr/>For<wbr/>Controlled<wbr/>Error</span></a>
<a href="../classes/generated.ProposalForAnotherMultisigError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Proposal<wbr/>For<wbr/>Another<wbr/>Multisig<wbr/>Error</span></a>
<a href="../classes/generated.ProtectedAccountError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Protected<wbr/>Account<wbr/>Error</span></a>
<a href="../classes/generated.RemoveLastMemberError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Remove<wbr/>Last<wbr/>Member<wbr/>Error</span></a>
<a href="../classes/generated.RentReclamationDisabledError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Rent<wbr/>Reclamation<wbr/>Disabled<wbr/>Error</span></a>
<a href="../classes/generated.SpendingLimitExceededError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Spending<wbr/>Limit<wbr/>Exceeded<wbr/>Error</span></a>
<a href="../classes/generated.SpendingLimitInvalidAmountError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Spending<wbr/>Limit<wbr/>Invalid<wbr/>Amount<wbr/>Error</span></a>
<a href="../classes/generated.StaleProposalError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Stale<wbr/>Proposal<wbr/>Error</span></a>
<a href="../classes/generated.TimeLockExceedsMaxAllowedError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Time<wbr/>Lock<wbr/>Exceeds<wbr/>Max<wbr/>Allowed<wbr/>Error</span></a>
<a href="../classes/generated.TimeLockNotReleasedError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Time<wbr/>Lock<wbr/>Not<wbr/>Released<wbr/>Error</span></a>
<a href="../classes/generated.TooManyMembersError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Too<wbr/>Many<wbr/>Members<wbr/>Error</span></a>
<a href="../classes/generated.TransactionForAnotherMultisigError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Transaction<wbr/>For<wbr/>Another<wbr/>Multisig<wbr/>Error</span></a>
<a href="../classes/generated.TransactionNotLastInBatchError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Transaction<wbr/>Not<wbr/>Last<wbr/>In<wbr/>Batch<wbr/>Error</span></a>
<a href="../classes/generated.TransactionNotMatchingProposalError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Transaction<wbr/>Not<wbr/>Matching<wbr/>Proposal<wbr/>Error</span></a>
<a href="../classes/generated.UnauthorizedError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Unauthorized<wbr/>Error</span></a>
<a href="../classes/generated.UnknownPermissionError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Unknown<wbr/>Permission<wbr/>Error</span></a>
<a href="../functions/generated.errorFromCode.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>error<wbr/>From<wbr/>Code</span></a>
<a href="../functions/generated.errorFromName.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>error<wbr/>From<wbr/>Name</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">Instructions</h3><div class="tsd-index-list"><a href="../types/generated.BatchAccountsCloseInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Batch<wbr/>Accounts<wbr/>Close<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.BatchAddTransactionInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Batch<wbr/>Add<wbr/>Transaction<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.BatchAddTransactionInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Batch<wbr/>Add<wbr/>Transaction<wbr/>Instruction<wbr/>Args</span></a>
<a href="../types/generated.BatchCreateInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Batch<wbr/>Create<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.BatchCreateInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Batch<wbr/>Create<wbr/>Instruction<wbr/>Args</span></a>
<a href="../types/generated.BatchExecuteTransactionInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Batch<wbr/>Execute<wbr/>Transaction<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.ConfigTransactionAccountsCloseInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Config<wbr/>Transaction<wbr/>Accounts<wbr/>Close<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.ConfigTransactionCreateInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Config<wbr/>Transaction<wbr/>Create<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.ConfigTransactionCreateInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Config<wbr/>Transaction<wbr/>Create<wbr/>Instruction<wbr/>Args</span></a>
<a href="../types/generated.ConfigTransactionExecuteInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Config<wbr/>Transaction<wbr/>Execute<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.MultisigAddMemberInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Add<wbr/>Member<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.MultisigAddMemberInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Add<wbr/>Member<wbr/>Instruction<wbr/>Args</span></a>
<a href="../types/generated.MultisigAddSpendingLimitInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Add<wbr/>Spending<wbr/>Limit<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.MultisigAddSpendingLimitInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Add<wbr/>Spending<wbr/>Limit<wbr/>Instruction<wbr/>Args</span></a>
<a href="../types/generated.MultisigChangeThresholdInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Change<wbr/>Threshold<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.MultisigChangeThresholdInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Change<wbr/>Threshold<wbr/>Instruction<wbr/>Args</span></a>
<a href="../types/generated.MultisigCreateInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Create<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.MultisigCreateInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Create<wbr/>Instruction<wbr/>Args</span></a>
<a href="../types/generated.MultisigCreateV2InstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Create<wbr/>V2<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.MultisigCreateV2InstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Create<wbr/>V2<wbr/>Instruction<wbr/>Args</span></a>
<a href="../types/generated.MultisigRemoveMemberInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Remove<wbr/>Member<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.MultisigRemoveMemberInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Remove<wbr/>Member<wbr/>Instruction<wbr/>Args</span></a>
<a href="../types/generated.MultisigRemoveSpendingLimitInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Remove<wbr/>Spending<wbr/>Limit<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.MultisigRemoveSpendingLimitInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Remove<wbr/>Spending<wbr/>Limit<wbr/>Instruction<wbr/>Args</span></a>
<a href="../types/generated.MultisigSetConfigAuthorityInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Set<wbr/>Config<wbr/>Authority<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.MultisigSetConfigAuthorityInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Set<wbr/>Config<wbr/>Authority<wbr/>Instruction<wbr/>Args</span></a>
<a href="../types/generated.MultisigSetRentCollectorInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Set<wbr/>Rent<wbr/>Collector<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.MultisigSetRentCollectorInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Set<wbr/>Rent<wbr/>Collector<wbr/>Instruction<wbr/>Args</span></a>
<a href="../types/generated.MultisigSetTimeLockInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Set<wbr/>Time<wbr/>Lock<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.MultisigSetTimeLockInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Set<wbr/>Time<wbr/>Lock<wbr/>Instruction<wbr/>Args</span></a>
<a href="../types/generated.ProgramConfigInitInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Program<wbr/>Config<wbr/>Init<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.ProgramConfigInitInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Program<wbr/>Config<wbr/>Init<wbr/>Instruction<wbr/>Args</span></a>
<a href="../types/generated.ProgramConfigSetAuthorityInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Program<wbr/>Config<wbr/>Set<wbr/>Authority<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.ProgramConfigSetAuthorityInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Program<wbr/>Config<wbr/>Set<wbr/>Authority<wbr/>Instruction<wbr/>Args</span></a>
<a href="../types/generated.ProgramConfigSetMultisigCreationFeeInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Program<wbr/>Config<wbr/>Set<wbr/>Multisig<wbr/>Creation<wbr/>Fee<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.ProgramConfigSetMultisigCreationFeeInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Program<wbr/>Config<wbr/>Set<wbr/>Multisig<wbr/>Creation<wbr/>Fee<wbr/>Instruction<wbr/>Args</span></a>
<a href="../types/generated.ProgramConfigSetTreasuryInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Program<wbr/>Config<wbr/>Set<wbr/>Treasury<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.ProgramConfigSetTreasuryInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Program<wbr/>Config<wbr/>Set<wbr/>Treasury<wbr/>Instruction<wbr/>Args</span></a>
<a href="../types/generated.ProposalActivateInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Proposal<wbr/>Activate<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.ProposalApproveInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Proposal<wbr/>Approve<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.ProposalApproveInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Proposal<wbr/>Approve<wbr/>Instruction<wbr/>Args</span></a>
<a href="../types/generated.ProposalCancelInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Proposal<wbr/>Cancel<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.ProposalCancelInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Proposal<wbr/>Cancel<wbr/>Instruction<wbr/>Args</span></a>
<a href="../types/generated.ProposalCreateInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Proposal<wbr/>Create<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.ProposalCreateInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Proposal<wbr/>Create<wbr/>Instruction<wbr/>Args</span></a>
<a href="../types/generated.ProposalRejectInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Proposal<wbr/>Reject<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.ProposalRejectInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Proposal<wbr/>Reject<wbr/>Instruction<wbr/>Args</span></a>
<a href="../types/generated.SpendingLimitUseInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Spending<wbr/>Limit<wbr/>Use<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.SpendingLimitUseInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Spending<wbr/>Limit<wbr/>Use<wbr/>Instruction<wbr/>Args</span></a>
<a href="../types/generated.VaultBatchTransactionAccountCloseInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Vault<wbr/>Batch<wbr/>Transaction<wbr/>Account<wbr/>Close<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.VaultTransactionAccountsCloseInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Vault<wbr/>Transaction<wbr/>Accounts<wbr/>Close<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.VaultTransactionCreateInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Vault<wbr/>Transaction<wbr/>Create<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.VaultTransactionCreateInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Vault<wbr/>Transaction<wbr/>Create<wbr/>Instruction<wbr/>Args</span></a>
<a href="../types/generated.VaultTransactionExecuteInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Vault<wbr/>Transaction<wbr/>Execute<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../variables/generated.batchAccountsCloseStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>batch<wbr/>Accounts<wbr/>Close<wbr/>Struct</span></a>
<a href="../variables/generated.batchAddTransactionStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>batch<wbr/>Add<wbr/>Transaction<wbr/>Struct</span></a>
<a href="../variables/generated.batchCreateStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>batch<wbr/>Create<wbr/>Struct</span></a>
<a href="../variables/generated.batchExecuteTransactionStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>batch<wbr/>Execute<wbr/>Transaction<wbr/>Struct</span></a>
<a href="../variables/generated.configTransactionAccountsCloseStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>config<wbr/>Transaction<wbr/>Accounts<wbr/>Close<wbr/>Struct</span></a>
<a href="../variables/generated.configTransactionCreateStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>config<wbr/>Transaction<wbr/>Create<wbr/>Struct</span></a>
<a href="../variables/generated.configTransactionExecuteStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>config<wbr/>Transaction<wbr/>Execute<wbr/>Struct</span></a>
<a href="../variables/generated.multisigAddMemberStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Add<wbr/>Member<wbr/>Struct</span></a>
<a href="../variables/generated.multisigAddSpendingLimitStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Add<wbr/>Spending<wbr/>Limit<wbr/>Struct</span></a>
<a href="../variables/generated.multisigChangeThresholdStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Change<wbr/>Threshold<wbr/>Struct</span></a>
<a href="../variables/generated.multisigCreateStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Create<wbr/>Struct</span></a>
<a href="../variables/generated.multisigCreateV2Struct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Create<wbr/>V2<wbr/>Struct</span></a>
<a href="../variables/generated.multisigRemoveMemberStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Remove<wbr/>Member<wbr/>Struct</span></a>
<a href="../variables/generated.multisigRemoveSpendingLimitStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Remove<wbr/>Spending<wbr/>Limit<wbr/>Struct</span></a>
<a href="../variables/generated.multisigSetConfigAuthorityStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Set<wbr/>Config<wbr/>Authority<wbr/>Struct</span></a>
<a href="../variables/generated.multisigSetRentCollectorStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Set<wbr/>Rent<wbr/>Collector<wbr/>Struct</span></a>
<a href="../variables/generated.multisigSetTimeLockStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Set<wbr/>Time<wbr/>Lock<wbr/>Struct</span></a>
<a href="../variables/generated.programConfigInitStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>program<wbr/>Config<wbr/>Init<wbr/>Struct</span></a>
<a href="../variables/generated.programConfigSetAuthorityStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>program<wbr/>Config<wbr/>Set<wbr/>Authority<wbr/>Struct</span></a>
<a href="../variables/generated.programConfigSetMultisigCreationFeeStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>program<wbr/>Config<wbr/>Set<wbr/>Multisig<wbr/>Creation<wbr/>Fee<wbr/>Struct</span></a>
<a href="../variables/generated.programConfigSetTreasuryStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>program<wbr/>Config<wbr/>Set<wbr/>Treasury<wbr/>Struct</span></a>
<a href="../variables/generated.proposalActivateStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>proposal<wbr/>Activate<wbr/>Struct</span></a>
<a href="../variables/generated.proposalApproveStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>proposal<wbr/>Approve<wbr/>Struct</span></a>
<a href="../variables/generated.proposalCancelStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>proposal<wbr/>Cancel<wbr/>Struct</span></a>
<a href="../variables/generated.proposalCreateStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>proposal<wbr/>Create<wbr/>Struct</span></a>
<a href="../variables/generated.proposalRejectStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>proposal<wbr/>Reject<wbr/>Struct</span></a>
<a href="../variables/generated.spendingLimitUseStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>spending<wbr/>Limit<wbr/>Use<wbr/>Struct</span></a>
<a href="../variables/generated.vaultBatchTransactionAccountCloseStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>vault<wbr/>Batch<wbr/>Transaction<wbr/>Account<wbr/>Close<wbr/>Struct</span></a>
<a href="../variables/generated.vaultTransactionAccountsCloseStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>vault<wbr/>Transaction<wbr/>Accounts<wbr/>Close<wbr/>Struct</span></a>
<a href="../variables/generated.vaultTransactionCreateStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>vault<wbr/>Transaction<wbr/>Create<wbr/>Struct</span></a>
<a href="../variables/generated.vaultTransactionExecuteStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>vault<wbr/>Transaction<wbr/>Execute<wbr/>Struct</span></a>
<a href="../functions/generated.createBatchAccountsCloseInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Batch<wbr/>Accounts<wbr/>Close<wbr/>Instruction</span></a>
<a href="../functions/generated.createBatchAddTransactionInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Batch<wbr/>Add<wbr/>Transaction<wbr/>Instruction</span></a>
<a href="../functions/generated.createBatchCreateInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Batch<wbr/>Create<wbr/>Instruction</span></a>
<a href="../functions/generated.createBatchExecuteTransactionInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Batch<wbr/>Execute<wbr/>Transaction<wbr/>Instruction</span></a>
<a href="../functions/generated.createConfigTransactionAccountsCloseInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Config<wbr/>Transaction<wbr/>Accounts<wbr/>Close<wbr/>Instruction</span></a>
<a href="../functions/generated.createConfigTransactionCreateInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Config<wbr/>Transaction<wbr/>Create<wbr/>Instruction</span></a>
<a href="../functions/generated.createConfigTransactionExecuteInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Config<wbr/>Transaction<wbr/>Execute<wbr/>Instruction</span></a>
<a href="../functions/generated.createMultisigAddMemberInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Multisig<wbr/>Add<wbr/>Member<wbr/>Instruction</span></a>
<a href="../functions/generated.createMultisigAddSpendingLimitInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Multisig<wbr/>Add<wbr/>Spending<wbr/>Limit<wbr/>Instruction</span></a>
<a href="../functions/generated.createMultisigChangeThresholdInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Multisig<wbr/>Change<wbr/>Threshold<wbr/>Instruction</span></a>
<a href="../functions/generated.createMultisigCreateInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Multisig<wbr/>Create<wbr/>Instruction</span></a>
<a href="../functions/generated.createMultisigCreateV2Instruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Multisig<wbr/>Create<wbr/>V2<wbr/>Instruction</span></a>
<a href="../functions/generated.createMultisigRemoveMemberInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Multisig<wbr/>Remove<wbr/>Member<wbr/>Instruction</span></a>
<a href="../functions/generated.createMultisigRemoveSpendingLimitInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Multisig<wbr/>Remove<wbr/>Spending<wbr/>Limit<wbr/>Instruction</span></a>
<a href="../functions/generated.createMultisigSetConfigAuthorityInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Multisig<wbr/>Set<wbr/>Config<wbr/>Authority<wbr/>Instruction</span></a>
<a href="../functions/generated.createMultisigSetRentCollectorInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Multisig<wbr/>Set<wbr/>Rent<wbr/>Collector<wbr/>Instruction</span></a>
<a href="../functions/generated.createMultisigSetTimeLockInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Multisig<wbr/>Set<wbr/>Time<wbr/>Lock<wbr/>Instruction</span></a>
<a href="../functions/generated.createProgramConfigInitInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Program<wbr/>Config<wbr/>Init<wbr/>Instruction</span></a>
<a href="../functions/generated.createProgramConfigSetAuthorityInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Program<wbr/>Config<wbr/>Set<wbr/>Authority<wbr/>Instruction</span></a>
<a href="../functions/generated.createProgramConfigSetMultisigCreationFeeInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Program<wbr/>Config<wbr/>Set<wbr/>Multisig<wbr/>Creation<wbr/>Fee<wbr/>Instruction</span></a>
<a href="../functions/generated.createProgramConfigSetTreasuryInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Program<wbr/>Config<wbr/>Set<wbr/>Treasury<wbr/>Instruction</span></a>
<a href="../functions/generated.createProposalActivateInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Proposal<wbr/>Activate<wbr/>Instruction</span></a>
<a href="../functions/generated.createProposalApproveInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Proposal<wbr/>Approve<wbr/>Instruction</span></a>
<a href="../functions/generated.createProposalCancelInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Proposal<wbr/>Cancel<wbr/>Instruction</span></a>
<a href="../functions/generated.createProposalCreateInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Proposal<wbr/>Create<wbr/>Instruction</span></a>
<a href="../functions/generated.createProposalRejectInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Proposal<wbr/>Reject<wbr/>Instruction</span></a>
<a href="../functions/generated.createSpendingLimitUseInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Spending<wbr/>Limit<wbr/>Use<wbr/>Instruction</span></a>
<a href="../functions/generated.createVaultBatchTransactionAccountCloseInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Vault<wbr/>Batch<wbr/>Transaction<wbr/>Account<wbr/>Close<wbr/>Instruction</span></a>
<a href="../functions/generated.createVaultTransactionAccountsCloseInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Vault<wbr/>Transaction<wbr/>Accounts<wbr/>Close<wbr/>Instruction</span></a>
<a href="../functions/generated.createVaultTransactionCreateInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Vault<wbr/>Transaction<wbr/>Create<wbr/>Instruction</span></a>
<a href="../functions/generated.createVaultTransactionExecuteInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Vault<wbr/>Transaction<wbr/>Execute<wbr/>Instruction</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">MultisigAddMember</h3><div class="tsd-index-list"><a href="../types/generated.MultisigAddMemberInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Add<wbr/>Member<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.MultisigAddMemberInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Add<wbr/>Member<wbr/>Instruction<wbr/>Args</span></a>
<a href="../variables/generated.multisigAddMemberStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Add<wbr/>Member<wbr/>Struct</span></a>
<a href="../functions/generated.createMultisigAddMemberInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Multisig<wbr/>Add<wbr/>Member<wbr/>Instruction</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">MultisigAddSpendingLimit</h3><div class="tsd-index-list"><a href="../types/generated.MultisigAddSpendingLimitInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Add<wbr/>Spending<wbr/>Limit<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.MultisigAddSpendingLimitInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Add<wbr/>Spending<wbr/>Limit<wbr/>Instruction<wbr/>Args</span></a>
<a href="../variables/generated.multisigAddSpendingLimitStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Add<wbr/>Spending<wbr/>Limit<wbr/>Struct</span></a>
<a href="../functions/generated.createMultisigAddSpendingLimitInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Multisig<wbr/>Add<wbr/>Spending<wbr/>Limit<wbr/>Instruction</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">MultisigChangeThreshold</h3><div class="tsd-index-list"><a href="../types/generated.MultisigChangeThresholdInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Change<wbr/>Threshold<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.MultisigChangeThresholdInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Change<wbr/>Threshold<wbr/>Instruction<wbr/>Args</span></a>
<a href="../variables/generated.multisigChangeThresholdStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Change<wbr/>Threshold<wbr/>Struct</span></a>
<a href="../functions/generated.createMultisigChangeThresholdInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Multisig<wbr/>Change<wbr/>Threshold<wbr/>Instruction</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">MultisigCreate</h3><div class="tsd-index-list"><a href="../types/generated.MultisigCreateInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Create<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.MultisigCreateInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Create<wbr/>Instruction<wbr/>Args</span></a>
<a href="../variables/generated.multisigCreateStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Create<wbr/>Struct</span></a>
<a href="../functions/generated.createMultisigCreateInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Multisig<wbr/>Create<wbr/>Instruction</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">MultisigCreateV2</h3><div class="tsd-index-list"><a href="../types/generated.MultisigCreateV2InstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Create<wbr/>V2<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.MultisigCreateV2InstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Create<wbr/>V2<wbr/>Instruction<wbr/>Args</span></a>
<a href="../variables/generated.multisigCreateV2Struct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Create<wbr/>V2<wbr/>Struct</span></a>
<a href="../functions/generated.createMultisigCreateV2Instruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Multisig<wbr/>Create<wbr/>V2<wbr/>Instruction</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">MultisigRemoveMember</h3><div class="tsd-index-list"><a href="../types/generated.MultisigRemoveMemberInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Remove<wbr/>Member<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.MultisigRemoveMemberInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Remove<wbr/>Member<wbr/>Instruction<wbr/>Args</span></a>
<a href="../variables/generated.multisigRemoveMemberStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Remove<wbr/>Member<wbr/>Struct</span></a>
<a href="../functions/generated.createMultisigRemoveMemberInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Multisig<wbr/>Remove<wbr/>Member<wbr/>Instruction</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">MultisigRemoveSpendingLimit</h3><div class="tsd-index-list"><a href="../types/generated.MultisigRemoveSpendingLimitInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Remove<wbr/>Spending<wbr/>Limit<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.MultisigRemoveSpendingLimitInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Remove<wbr/>Spending<wbr/>Limit<wbr/>Instruction<wbr/>Args</span></a>
<a href="../variables/generated.multisigRemoveSpendingLimitStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Remove<wbr/>Spending<wbr/>Limit<wbr/>Struct</span></a>
<a href="../functions/generated.createMultisigRemoveSpendingLimitInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Multisig<wbr/>Remove<wbr/>Spending<wbr/>Limit<wbr/>Instruction</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">MultisigSetConfigAuthority</h3><div class="tsd-index-list"><a href="../types/generated.MultisigSetConfigAuthorityInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Set<wbr/>Config<wbr/>Authority<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.MultisigSetConfigAuthorityInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Set<wbr/>Config<wbr/>Authority<wbr/>Instruction<wbr/>Args</span></a>
<a href="../variables/generated.multisigSetConfigAuthorityStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Set<wbr/>Config<wbr/>Authority<wbr/>Struct</span></a>
<a href="../functions/generated.createMultisigSetConfigAuthorityInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Multisig<wbr/>Set<wbr/>Config<wbr/>Authority<wbr/>Instruction</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">MultisigSetRentCollector</h3><div class="tsd-index-list"><a href="../types/generated.MultisigSetRentCollectorInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Set<wbr/>Rent<wbr/>Collector<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.MultisigSetRentCollectorInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Set<wbr/>Rent<wbr/>Collector<wbr/>Instruction<wbr/>Args</span></a>
<a href="../variables/generated.multisigSetRentCollectorStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Set<wbr/>Rent<wbr/>Collector<wbr/>Struct</span></a>
<a href="../functions/generated.createMultisigSetRentCollectorInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Multisig<wbr/>Set<wbr/>Rent<wbr/>Collector<wbr/>Instruction</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">MultisigSetTimeLock</h3><div class="tsd-index-list"><a href="../types/generated.MultisigSetTimeLockInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Set<wbr/>Time<wbr/>Lock<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.MultisigSetTimeLockInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Set<wbr/>Time<wbr/>Lock<wbr/>Instruction<wbr/>Args</span></a>
<a href="../variables/generated.multisigSetTimeLockStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Set<wbr/>Time<wbr/>Lock<wbr/>Struct</span></a>
<a href="../functions/generated.createMultisigSetTimeLockInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Multisig<wbr/>Set<wbr/>Time<wbr/>Lock<wbr/>Instruction</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">Other</h3><div class="tsd-index-list"><a href="../types/generated.BatchAddTransactionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Batch<wbr/>Add<wbr/>Transaction<wbr/>Args</span></a>
<a href="../types/generated.BatchCreateArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Batch<wbr/>Create<wbr/>Args</span></a>
<a href="../types/generated.ConfigTransactionCreateArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Config<wbr/>Transaction<wbr/>Create<wbr/>Args</span></a>
<a href="../types/generated.Member.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Member</span></a>
<a href="../types/generated.MultisigAddMemberArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Add<wbr/>Member<wbr/>Args</span></a>
<a href="../types/generated.MultisigAddSpendingLimitArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Add<wbr/>Spending<wbr/>Limit<wbr/>Args</span></a>
<a href="../types/generated.MultisigChangeThresholdArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Change<wbr/>Threshold<wbr/>Args</span></a>
<a href="../types/generated.MultisigCompiledInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Compiled<wbr/>Instruction</span></a>
<a href="../types/generated.MultisigCreateArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Create<wbr/>Args</span></a>
<a href="../types/generated.MultisigCreateArgsV2.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Create<wbr/>Args<wbr/>V2</span></a>
<a href="../types/generated.MultisigMessageAddressTableLookup.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Message<wbr/>Address<wbr/>Table<wbr/>Lookup</span></a>
<a href="../types/generated.MultisigRemoveMemberArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Remove<wbr/>Member<wbr/>Args</span></a>
<a href="../types/generated.MultisigRemoveSpendingLimitArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Remove<wbr/>Spending<wbr/>Limit<wbr/>Args</span></a>
<a href="../types/generated.MultisigSetConfigAuthorityArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Set<wbr/>Config<wbr/>Authority<wbr/>Args</span></a>
<a href="../types/generated.MultisigSetRentCollectorArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Set<wbr/>Rent<wbr/>Collector<wbr/>Args</span></a>
<a href="../types/generated.MultisigSetTimeLockArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Set<wbr/>Time<wbr/>Lock<wbr/>Args</span></a>
<a href="../types/generated.Permissions.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Permissions</span></a>
<a href="../types/generated.ProgramConfigInitArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Program<wbr/>Config<wbr/>Init<wbr/>Args</span></a>
<a href="../types/generated.ProgramConfigSetAuthorityArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Program<wbr/>Config<wbr/>Set<wbr/>Authority<wbr/>Args</span></a>
<a href="../types/generated.ProgramConfigSetMultisigCreationFeeArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Program<wbr/>Config<wbr/>Set<wbr/>Multisig<wbr/>Creation<wbr/>Fee<wbr/>Args</span></a>
<a href="../types/generated.ProgramConfigSetTreasuryArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Program<wbr/>Config<wbr/>Set<wbr/>Treasury<wbr/>Args</span></a>
<a href="../types/generated.ProposalCreateArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Proposal<wbr/>Create<wbr/>Args</span></a>
<a href="../types/generated.ProposalVoteArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Proposal<wbr/>Vote<wbr/>Args</span></a>
<a href="../types/generated.SpendingLimitUseArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Spending<wbr/>Limit<wbr/>Use<wbr/>Args</span></a>
<a href="../types/generated.VaultTransactionCreateArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Vault<wbr/>Transaction<wbr/>Create<wbr/>Args</span></a>
<a href="../types/generated.VaultTransactionMessage.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Vault<wbr/>Transaction<wbr/>Message</span></a>
<a href="../variables/generated.accountProviders.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>account<wbr/>Providers</span></a>
<a href="../variables/generated.batchAccountsCloseInstructionDiscriminator.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>batch<wbr/>Accounts<wbr/>Close<wbr/>Instruction<wbr/>Discriminator</span></a>
<a href="../variables/generated.batchAddTransactionInstructionDiscriminator.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>batch<wbr/>Add<wbr/>Transaction<wbr/>Instruction<wbr/>Discriminator</span></a>
<a href="../variables/generated.batchCreateInstructionDiscriminator.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>batch<wbr/>Create<wbr/>Instruction<wbr/>Discriminator</span></a>
<a href="../variables/generated.batchDiscriminator.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>batch<wbr/>Discriminator</span></a>
<a href="../variables/generated.batchExecuteTransactionInstructionDiscriminator.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>batch<wbr/>Execute<wbr/>Transaction<wbr/>Instruction<wbr/>Discriminator</span></a>
<a href="../variables/generated.configTransactionAccountsCloseInstructionDiscriminator.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>config<wbr/>Transaction<wbr/>Accounts<wbr/>Close<wbr/>Instruction<wbr/>Discriminator</span></a>
<a href="../variables/generated.configTransactionCreateInstructionDiscriminator.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>config<wbr/>Transaction<wbr/>Create<wbr/>Instruction<wbr/>Discriminator</span></a>
<a href="../variables/generated.configTransactionDiscriminator.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>config<wbr/>Transaction<wbr/>Discriminator</span></a>
<a href="../variables/generated.configTransactionExecuteInstructionDiscriminator.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>config<wbr/>Transaction<wbr/>Execute<wbr/>Instruction<wbr/>Discriminator</span></a>
<a href="../variables/generated.multisigAddMemberInstructionDiscriminator.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Add<wbr/>Member<wbr/>Instruction<wbr/>Discriminator</span></a>
<a href="../variables/generated.multisigAddSpendingLimitInstructionDiscriminator.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Add<wbr/>Spending<wbr/>Limit<wbr/>Instruction<wbr/>Discriminator</span></a>
<a href="../variables/generated.multisigChangeThresholdInstructionDiscriminator.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Change<wbr/>Threshold<wbr/>Instruction<wbr/>Discriminator</span></a>
<a href="../variables/generated.multisigCreateInstructionDiscriminator.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Create<wbr/>Instruction<wbr/>Discriminator</span></a>
<a href="../variables/generated.multisigCreateV2InstructionDiscriminator.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Create<wbr/>V2<wbr/>Instruction<wbr/>Discriminator</span></a>
<a href="../variables/generated.multisigDiscriminator.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Discriminator</span></a>
<a href="../variables/generated.multisigRemoveMemberInstructionDiscriminator.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Remove<wbr/>Member<wbr/>Instruction<wbr/>Discriminator</span></a>
<a href="../variables/generated.multisigRemoveSpendingLimitInstructionDiscriminator.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Remove<wbr/>Spending<wbr/>Limit<wbr/>Instruction<wbr/>Discriminator</span></a>
<a href="../variables/generated.multisigSetConfigAuthorityInstructionDiscriminator.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Set<wbr/>Config<wbr/>Authority<wbr/>Instruction<wbr/>Discriminator</span></a>
<a href="../variables/generated.multisigSetRentCollectorInstructionDiscriminator.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Set<wbr/>Rent<wbr/>Collector<wbr/>Instruction<wbr/>Discriminator</span></a>
<a href="../variables/generated.multisigSetTimeLockInstructionDiscriminator.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Set<wbr/>Time<wbr/>Lock<wbr/>Instruction<wbr/>Discriminator</span></a>
<a href="../variables/generated.programConfigDiscriminator.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>program<wbr/>Config<wbr/>Discriminator</span></a>
<a href="../variables/generated.programConfigInitInstructionDiscriminator.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>program<wbr/>Config<wbr/>Init<wbr/>Instruction<wbr/>Discriminator</span></a>
<a href="../variables/generated.programConfigSetAuthorityInstructionDiscriminator.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>program<wbr/>Config<wbr/>Set<wbr/>Authority<wbr/>Instruction<wbr/>Discriminator</span></a>
<a href="../variables/generated.programConfigSetMultisigCreationFeeInstructionDiscriminator.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>program<wbr/>Config<wbr/>Set<wbr/>Multisig<wbr/>Creation<wbr/>Fee<wbr/>Instruction<wbr/>Discriminator</span></a>
<a href="../variables/generated.programConfigSetTreasuryInstructionDiscriminator.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>program<wbr/>Config<wbr/>Set<wbr/>Treasury<wbr/>Instruction<wbr/>Discriminator</span></a>
<a href="../variables/generated.proposalActivateInstructionDiscriminator.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>proposal<wbr/>Activate<wbr/>Instruction<wbr/>Discriminator</span></a>
<a href="../variables/generated.proposalApproveInstructionDiscriminator.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>proposal<wbr/>Approve<wbr/>Instruction<wbr/>Discriminator</span></a>
<a href="../variables/generated.proposalCancelInstructionDiscriminator.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>proposal<wbr/>Cancel<wbr/>Instruction<wbr/>Discriminator</span></a>
<a href="../variables/generated.proposalCreateInstructionDiscriminator.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>proposal<wbr/>Create<wbr/>Instruction<wbr/>Discriminator</span></a>
<a href="../variables/generated.proposalDiscriminator.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>proposal<wbr/>Discriminator</span></a>
<a href="../variables/generated.proposalRejectInstructionDiscriminator.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>proposal<wbr/>Reject<wbr/>Instruction<wbr/>Discriminator</span></a>
<a href="../variables/generated.spendingLimitDiscriminator.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>spending<wbr/>Limit<wbr/>Discriminator</span></a>
<a href="../variables/generated.spendingLimitUseInstructionDiscriminator.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>spending<wbr/>Limit<wbr/>Use<wbr/>Instruction<wbr/>Discriminator</span></a>
<a href="../variables/generated.vaultBatchTransactionAccountCloseInstructionDiscriminator.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>vault<wbr/>Batch<wbr/>Transaction<wbr/>Account<wbr/>Close<wbr/>Instruction<wbr/>Discriminator</span></a>
<a href="../variables/generated.vaultBatchTransactionDiscriminator.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>vault<wbr/>Batch<wbr/>Transaction<wbr/>Discriminator</span></a>
<a href="../variables/generated.vaultTransactionAccountsCloseInstructionDiscriminator.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>vault<wbr/>Transaction<wbr/>Accounts<wbr/>Close<wbr/>Instruction<wbr/>Discriminator</span></a>
<a href="../variables/generated.vaultTransactionCreateInstructionDiscriminator.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>vault<wbr/>Transaction<wbr/>Create<wbr/>Instruction<wbr/>Discriminator</span></a>
<a href="../variables/generated.vaultTransactionDiscriminator.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>vault<wbr/>Transaction<wbr/>Discriminator</span></a>
<a href="../variables/generated.vaultTransactionExecuteInstructionDiscriminator.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>vault<wbr/>Transaction<wbr/>Execute<wbr/>Instruction<wbr/>Discriminator</span></a>
<a href="../functions/generated.isConfigActionAddMember.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>is<wbr/>Config<wbr/>Action<wbr/>Add<wbr/>Member</span></a>
<a href="../functions/generated.isConfigActionAddSpendingLimit.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>is<wbr/>Config<wbr/>Action<wbr/>Add<wbr/>Spending<wbr/>Limit</span></a>
<a href="../functions/generated.isConfigActionChangeThreshold.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>is<wbr/>Config<wbr/>Action<wbr/>Change<wbr/>Threshold</span></a>
<a href="../functions/generated.isConfigActionRemoveMember.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>is<wbr/>Config<wbr/>Action<wbr/>Remove<wbr/>Member</span></a>
<a href="../functions/generated.isConfigActionRemoveSpendingLimit.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>is<wbr/>Config<wbr/>Action<wbr/>Remove<wbr/>Spending<wbr/>Limit</span></a>
<a href="../functions/generated.isConfigActionSetRentCollector.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>is<wbr/>Config<wbr/>Action<wbr/>Set<wbr/>Rent<wbr/>Collector</span></a>
<a href="../functions/generated.isConfigActionSetTimeLock.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>is<wbr/>Config<wbr/>Action<wbr/>Set<wbr/>Time<wbr/>Lock</span></a>
<a href="../functions/generated.isProposalStatusActive.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>is<wbr/>Proposal<wbr/>Status<wbr/>Active</span></a>
<a href="../functions/generated.isProposalStatusApproved.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>is<wbr/>Proposal<wbr/>Status<wbr/>Approved</span></a>
<a href="../functions/generated.isProposalStatusCancelled.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>is<wbr/>Proposal<wbr/>Status<wbr/>Cancelled</span></a>
<a href="../functions/generated.isProposalStatusDraft.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>is<wbr/>Proposal<wbr/>Status<wbr/>Draft</span></a>
<a href="../functions/generated.isProposalStatusExecuted.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>is<wbr/>Proposal<wbr/>Status<wbr/>Executed</span></a>
<a href="../functions/generated.isProposalStatusExecuting.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>is<wbr/>Proposal<wbr/>Status<wbr/>Executing</span></a>
<a href="../functions/generated.isProposalStatusRejected.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>is<wbr/>Proposal<wbr/>Status<wbr/>Rejected</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">ProgramConfigInit</h3><div class="tsd-index-list"><a href="../types/generated.ProgramConfigInitInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Program<wbr/>Config<wbr/>Init<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.ProgramConfigInitInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Program<wbr/>Config<wbr/>Init<wbr/>Instruction<wbr/>Args</span></a>
<a href="../variables/generated.programConfigInitStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>program<wbr/>Config<wbr/>Init<wbr/>Struct</span></a>
<a href="../functions/generated.createProgramConfigInitInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Program<wbr/>Config<wbr/>Init<wbr/>Instruction</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">ProgramConfigSetAuthority</h3><div class="tsd-index-list"><a href="../types/generated.ProgramConfigSetAuthorityInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Program<wbr/>Config<wbr/>Set<wbr/>Authority<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.ProgramConfigSetAuthorityInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Program<wbr/>Config<wbr/>Set<wbr/>Authority<wbr/>Instruction<wbr/>Args</span></a>
<a href="../variables/generated.programConfigSetAuthorityStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>program<wbr/>Config<wbr/>Set<wbr/>Authority<wbr/>Struct</span></a>
<a href="../functions/generated.createProgramConfigSetAuthorityInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Program<wbr/>Config<wbr/>Set<wbr/>Authority<wbr/>Instruction</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">ProgramConfigSetMultisigCreationFee</h3><div class="tsd-index-list"><a href="../types/generated.ProgramConfigSetMultisigCreationFeeInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Program<wbr/>Config<wbr/>Set<wbr/>Multisig<wbr/>Creation<wbr/>Fee<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.ProgramConfigSetMultisigCreationFeeInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Program<wbr/>Config<wbr/>Set<wbr/>Multisig<wbr/>Creation<wbr/>Fee<wbr/>Instruction<wbr/>Args</span></a>
<a href="../variables/generated.programConfigSetMultisigCreationFeeStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>program<wbr/>Config<wbr/>Set<wbr/>Multisig<wbr/>Creation<wbr/>Fee<wbr/>Struct</span></a>
<a href="../functions/generated.createProgramConfigSetMultisigCreationFeeInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Program<wbr/>Config<wbr/>Set<wbr/>Multisig<wbr/>Creation<wbr/>Fee<wbr/>Instruction</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">ProgramConfigSetTreasury</h3><div class="tsd-index-list"><a href="../types/generated.ProgramConfigSetTreasuryInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Program<wbr/>Config<wbr/>Set<wbr/>Treasury<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.ProgramConfigSetTreasuryInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Program<wbr/>Config<wbr/>Set<wbr/>Treasury<wbr/>Instruction<wbr/>Args</span></a>
<a href="../variables/generated.programConfigSetTreasuryStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>program<wbr/>Config<wbr/>Set<wbr/>Treasury<wbr/>Struct</span></a>
<a href="../functions/generated.createProgramConfigSetTreasuryInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Program<wbr/>Config<wbr/>Set<wbr/>Treasury<wbr/>Instruction</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">ProposalActivate</h3><div class="tsd-index-list"><a href="../types/generated.ProposalActivateInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Proposal<wbr/>Activate<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../variables/generated.proposalActivateStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>proposal<wbr/>Activate<wbr/>Struct</span></a>
<a href="../functions/generated.createProposalActivateInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Proposal<wbr/>Activate<wbr/>Instruction</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">ProposalApprove</h3><div class="tsd-index-list"><a href="../types/generated.ProposalApproveInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Proposal<wbr/>Approve<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.ProposalApproveInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Proposal<wbr/>Approve<wbr/>Instruction<wbr/>Args</span></a>
<a href="../variables/generated.proposalApproveStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>proposal<wbr/>Approve<wbr/>Struct</span></a>
<a href="../functions/generated.createProposalApproveInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Proposal<wbr/>Approve<wbr/>Instruction</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">ProposalCancel</h3><div class="tsd-index-list"><a href="../types/generated.ProposalCancelInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Proposal<wbr/>Cancel<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.ProposalCancelInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Proposal<wbr/>Cancel<wbr/>Instruction<wbr/>Args</span></a>
<a href="../variables/generated.proposalCancelStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>proposal<wbr/>Cancel<wbr/>Struct</span></a>
<a href="../functions/generated.createProposalCancelInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Proposal<wbr/>Cancel<wbr/>Instruction</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">ProposalCreate</h3><div class="tsd-index-list"><a href="../types/generated.ProposalCreateInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Proposal<wbr/>Create<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.ProposalCreateInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Proposal<wbr/>Create<wbr/>Instruction<wbr/>Args</span></a>
<a href="../variables/generated.proposalCreateStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>proposal<wbr/>Create<wbr/>Struct</span></a>
<a href="../functions/generated.createProposalCreateInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Proposal<wbr/>Create<wbr/>Instruction</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">ProposalReject</h3><div class="tsd-index-list"><a href="../types/generated.ProposalRejectInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Proposal<wbr/>Reject<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.ProposalRejectInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Proposal<wbr/>Reject<wbr/>Instruction<wbr/>Args</span></a>
<a href="../variables/generated.proposalRejectStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>proposal<wbr/>Reject<wbr/>Struct</span></a>
<a href="../functions/generated.createProposalRejectInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Proposal<wbr/>Reject<wbr/>Instruction</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">SpendingLimitUse</h3><div class="tsd-index-list"><a href="../types/generated.SpendingLimitUseInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Spending<wbr/>Limit<wbr/>Use<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.SpendingLimitUseInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Spending<wbr/>Limit<wbr/>Use<wbr/>Instruction<wbr/>Args</span></a>
<a href="../variables/generated.spendingLimitUseStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>spending<wbr/>Limit<wbr/>Use<wbr/>Struct</span></a>
<a href="../functions/generated.createSpendingLimitUseInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Spending<wbr/>Limit<wbr/>Use<wbr/>Instruction</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">VaultBatchTransactionAccountClose</h3><div class="tsd-index-list"><a href="../types/generated.VaultBatchTransactionAccountCloseInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Vault<wbr/>Batch<wbr/>Transaction<wbr/>Account<wbr/>Close<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../variables/generated.vaultBatchTransactionAccountCloseStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>vault<wbr/>Batch<wbr/>Transaction<wbr/>Account<wbr/>Close<wbr/>Struct</span></a>
<a href="../functions/generated.createVaultBatchTransactionAccountCloseInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Vault<wbr/>Batch<wbr/>Transaction<wbr/>Account<wbr/>Close<wbr/>Instruction</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">VaultTransactionAccountsClose</h3><div class="tsd-index-list"><a href="../types/generated.VaultTransactionAccountsCloseInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Vault<wbr/>Transaction<wbr/>Accounts<wbr/>Close<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../variables/generated.vaultTransactionAccountsCloseStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>vault<wbr/>Transaction<wbr/>Accounts<wbr/>Close<wbr/>Struct</span></a>
<a href="../functions/generated.createVaultTransactionAccountsCloseInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Vault<wbr/>Transaction<wbr/>Accounts<wbr/>Close<wbr/>Instruction</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">VaultTransactionCreate</h3><div class="tsd-index-list"><a href="../types/generated.VaultTransactionCreateInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Vault<wbr/>Transaction<wbr/>Create<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.VaultTransactionCreateInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Vault<wbr/>Transaction<wbr/>Create<wbr/>Instruction<wbr/>Args</span></a>
<a href="../variables/generated.vaultTransactionCreateStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>vault<wbr/>Transaction<wbr/>Create<wbr/>Struct</span></a>
<a href="../functions/generated.createVaultTransactionCreateInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Vault<wbr/>Transaction<wbr/>Create<wbr/>Instruction</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">VaultTransactionExecute</h3><div class="tsd-index-list"><a href="../types/generated.VaultTransactionExecuteInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Vault<wbr/>Transaction<wbr/>Execute<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../variables/generated.vaultTransactionExecuteStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>vault<wbr/>Transaction<wbr/>Execute<wbr/>Struct</span></a>
<a href="../functions/generated.createVaultTransactionExecuteInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Vault<wbr/>Transaction<wbr/>Execute<wbr/>Instruction</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">constants</h3><div class="tsd-index-list"><a href="../variables/generated.PROGRAM_ADDRESS.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>PROGRAM_<wbr/>ADDRESS</span></a>
<a href="../variables/generated.PROGRAM_ID.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>PROGRAM_<wbr/>ID</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">enums</h3><div class="tsd-index-list"><a href="../enums/generated.Period.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-8"></use></svg><span>Period</span></a>
<a href="../enums/generated.Vote.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-8"></use></svg><span>Vote</span></a>
<a href="../types/generated.ConfigAction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Config<wbr/>Action</span></a>
<a href="../types/generated.ConfigActionRecord.html" class="tsd-index-link tsd-is-private"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Config<wbr/>Action<wbr/>Record</span></a>
<a href="../types/generated.ProposalStatus.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Proposal<wbr/>Status</span></a>
<a href="../types/generated.ProposalStatusRecord.html" class="tsd-index-link tsd-is-private"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Proposal<wbr/>Status<wbr/>Record</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">generated</h3><div class="tsd-index-list"><a href="../enums/generated.Period.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-8"></use></svg><span>Period</span></a>
<a href="../enums/generated.Vote.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-8"></use></svg><span>Vote</span></a>
<a href="../classes/generated.AlreadyApprovedError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Already<wbr/>Approved<wbr/>Error</span></a>
<a href="../classes/generated.AlreadyCancelledError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Already<wbr/>Cancelled<wbr/>Error</span></a>
<a href="../classes/generated.AlreadyRejectedError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Already<wbr/>Rejected<wbr/>Error</span></a>
<a href="../classes/generated.Batch.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Batch</span></a>
<a href="../classes/generated.BatchNotEmptyError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Batch<wbr/>Not<wbr/>Empty<wbr/>Error</span></a>
<a href="../classes/generated.ConfigTransaction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Config<wbr/>Transaction</span></a>
<a href="../classes/generated.DecimalsMismatchError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Decimals<wbr/>Mismatch<wbr/>Error</span></a>
<a href="../classes/generated.DuplicateMemberError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Duplicate<wbr/>Member<wbr/>Error</span></a>
<a href="../classes/generated.EmptyMembersError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Empty<wbr/>Members<wbr/>Error</span></a>
<a href="../classes/generated.IllegalAccountOwnerError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Illegal<wbr/>Account<wbr/>Owner<wbr/>Error</span></a>
<a href="../classes/generated.InvalidAccountError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Invalid<wbr/>Account<wbr/>Error</span></a>
<a href="../classes/generated.InvalidDestinationError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Invalid<wbr/>Destination<wbr/>Error</span></a>
<a href="../classes/generated.InvalidMintError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Invalid<wbr/>Mint<wbr/>Error</span></a>
<a href="../classes/generated.InvalidNumberOfAccountsError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Invalid<wbr/>Number<wbr/>Of<wbr/>Accounts<wbr/>Error</span></a>
<a href="../classes/generated.InvalidProposalStatusError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Invalid<wbr/>Proposal<wbr/>Status<wbr/>Error</span></a>
<a href="../classes/generated.InvalidRentCollectorError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Invalid<wbr/>Rent<wbr/>Collector<wbr/>Error</span></a>
<a href="../classes/generated.InvalidStaleTransactionIndexError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Invalid<wbr/>Stale<wbr/>Transaction<wbr/>Index<wbr/>Error</span></a>
<a href="../classes/generated.InvalidThresholdError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Invalid<wbr/>Threshold<wbr/>Error</span></a>
<a href="../classes/generated.InvalidTransactionIndexError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Invalid<wbr/>Transaction<wbr/>Index<wbr/>Error</span></a>
<a href="../classes/generated.InvalidTransactionMessageError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Invalid<wbr/>Transaction<wbr/>Message<wbr/>Error</span></a>
<a href="../classes/generated.MissingAccountError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Missing<wbr/>Account<wbr/>Error</span></a>
<a href="../classes/generated.Multisig.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Multisig</span></a>
<a href="../classes/generated.NoActionsError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>No<wbr/>Actions<wbr/>Error</span></a>
<a href="../classes/generated.NoExecutorsError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>No<wbr/>Executors<wbr/>Error</span></a>
<a href="../classes/generated.NoProposersError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>No<wbr/>Proposers<wbr/>Error</span></a>
<a href="../classes/generated.NoVotersError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>No<wbr/>Voters<wbr/>Error</span></a>
<a href="../classes/generated.NotAMemberError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>NotAMember<wbr/>Error</span></a>
<a href="../classes/generated.NotSupportedForControlledError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Not<wbr/>Supported<wbr/>For<wbr/>Controlled<wbr/>Error</span></a>
<a href="../classes/generated.ProgramConfig.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Program<wbr/>Config</span></a>
<a href="../classes/generated.Proposal.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Proposal</span></a>
<a href="../classes/generated.ProposalForAnotherMultisigError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Proposal<wbr/>For<wbr/>Another<wbr/>Multisig<wbr/>Error</span></a>
<a href="../classes/generated.ProtectedAccountError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Protected<wbr/>Account<wbr/>Error</span></a>
<a href="../classes/generated.RemoveLastMemberError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Remove<wbr/>Last<wbr/>Member<wbr/>Error</span></a>
<a href="../classes/generated.RentReclamationDisabledError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Rent<wbr/>Reclamation<wbr/>Disabled<wbr/>Error</span></a>
<a href="../classes/generated.SpendingLimit.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Spending<wbr/>Limit</span></a>
<a href="../classes/generated.SpendingLimitExceededError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Spending<wbr/>Limit<wbr/>Exceeded<wbr/>Error</span></a>
<a href="../classes/generated.SpendingLimitInvalidAmountError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Spending<wbr/>Limit<wbr/>Invalid<wbr/>Amount<wbr/>Error</span></a>
<a href="../classes/generated.StaleProposalError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Stale<wbr/>Proposal<wbr/>Error</span></a>
<a href="../classes/generated.TimeLockExceedsMaxAllowedError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Time<wbr/>Lock<wbr/>Exceeds<wbr/>Max<wbr/>Allowed<wbr/>Error</span></a>
<a href="../classes/generated.TimeLockNotReleasedError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Time<wbr/>Lock<wbr/>Not<wbr/>Released<wbr/>Error</span></a>
<a href="../classes/generated.TooManyMembersError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Too<wbr/>Many<wbr/>Members<wbr/>Error</span></a>
<a href="../classes/generated.TransactionForAnotherMultisigError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Transaction<wbr/>For<wbr/>Another<wbr/>Multisig<wbr/>Error</span></a>
<a href="../classes/generated.TransactionNotLastInBatchError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Transaction<wbr/>Not<wbr/>Last<wbr/>In<wbr/>Batch<wbr/>Error</span></a>
<a href="../classes/generated.TransactionNotMatchingProposalError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Transaction<wbr/>Not<wbr/>Matching<wbr/>Proposal<wbr/>Error</span></a>
<a href="../classes/generated.UnauthorizedError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Unauthorized<wbr/>Error</span></a>
<a href="../classes/generated.UnknownPermissionError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Unknown<wbr/>Permission<wbr/>Error</span></a>
<a href="../classes/generated.VaultBatchTransaction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Vault<wbr/>Batch<wbr/>Transaction</span></a>
<a href="../classes/generated.VaultTransaction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Vault<wbr/>Transaction</span></a>
<a href="../types/generated.BatchAccountsCloseInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Batch<wbr/>Accounts<wbr/>Close<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.BatchAddTransactionInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Batch<wbr/>Add<wbr/>Transaction<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.BatchAddTransactionInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Batch<wbr/>Add<wbr/>Transaction<wbr/>Instruction<wbr/>Args</span></a>
<a href="../types/generated.BatchArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Batch<wbr/>Args</span></a>
<a href="../types/generated.BatchCreateInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Batch<wbr/>Create<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.BatchCreateInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Batch<wbr/>Create<wbr/>Instruction<wbr/>Args</span></a>
<a href="../types/generated.BatchExecuteTransactionInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Batch<wbr/>Execute<wbr/>Transaction<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.ConfigAction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Config<wbr/>Action</span></a>
<a href="../types/generated.ConfigActionRecord.html" class="tsd-index-link tsd-is-private"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Config<wbr/>Action<wbr/>Record</span></a>
<a href="../types/generated.ConfigTransactionAccountsCloseInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Config<wbr/>Transaction<wbr/>Accounts<wbr/>Close<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.ConfigTransactionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Config<wbr/>Transaction<wbr/>Args</span></a>
<a href="../types/generated.ConfigTransactionCreateInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Config<wbr/>Transaction<wbr/>Create<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.ConfigTransactionCreateInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Config<wbr/>Transaction<wbr/>Create<wbr/>Instruction<wbr/>Args</span></a>
<a href="../types/generated.ConfigTransactionExecuteInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Config<wbr/>Transaction<wbr/>Execute<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.MultisigAddMemberInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Add<wbr/>Member<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.MultisigAddMemberInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Add<wbr/>Member<wbr/>Instruction<wbr/>Args</span></a>
<a href="../types/generated.MultisigAddSpendingLimitInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Add<wbr/>Spending<wbr/>Limit<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.MultisigAddSpendingLimitInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Add<wbr/>Spending<wbr/>Limit<wbr/>Instruction<wbr/>Args</span></a>
<a href="../types/generated.MultisigArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Args</span></a>
<a href="../types/generated.MultisigChangeThresholdInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Change<wbr/>Threshold<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.MultisigChangeThresholdInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Change<wbr/>Threshold<wbr/>Instruction<wbr/>Args</span></a>
<a href="../types/generated.MultisigCreateInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Create<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.MultisigCreateInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Create<wbr/>Instruction<wbr/>Args</span></a>
<a href="../types/generated.MultisigCreateV2InstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Create<wbr/>V2<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.MultisigCreateV2InstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Create<wbr/>V2<wbr/>Instruction<wbr/>Args</span></a>
<a href="../types/generated.MultisigRemoveMemberInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Remove<wbr/>Member<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.MultisigRemoveMemberInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Remove<wbr/>Member<wbr/>Instruction<wbr/>Args</span></a>
<a href="../types/generated.MultisigRemoveSpendingLimitInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Remove<wbr/>Spending<wbr/>Limit<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.MultisigRemoveSpendingLimitInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Remove<wbr/>Spending<wbr/>Limit<wbr/>Instruction<wbr/>Args</span></a>
<a href="../types/generated.MultisigSetConfigAuthorityInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Set<wbr/>Config<wbr/>Authority<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.MultisigSetConfigAuthorityInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Set<wbr/>Config<wbr/>Authority<wbr/>Instruction<wbr/>Args</span></a>
<a href="../types/generated.MultisigSetRentCollectorInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Set<wbr/>Rent<wbr/>Collector<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.MultisigSetRentCollectorInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Set<wbr/>Rent<wbr/>Collector<wbr/>Instruction<wbr/>Args</span></a>
<a href="../types/generated.MultisigSetTimeLockInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Set<wbr/>Time<wbr/>Lock<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.MultisigSetTimeLockInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Multisig<wbr/>Set<wbr/>Time<wbr/>Lock<wbr/>Instruction<wbr/>Args</span></a>
<a href="../types/generated.ProgramConfigArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Program<wbr/>Config<wbr/>Args</span></a>
<a href="../types/generated.ProgramConfigInitInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Program<wbr/>Config<wbr/>Init<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.ProgramConfigInitInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Program<wbr/>Config<wbr/>Init<wbr/>Instruction<wbr/>Args</span></a>
<a href="../types/generated.ProgramConfigSetAuthorityInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Program<wbr/>Config<wbr/>Set<wbr/>Authority<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.ProgramConfigSetAuthorityInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Program<wbr/>Config<wbr/>Set<wbr/>Authority<wbr/>Instruction<wbr/>Args</span></a>
<a href="../types/generated.ProgramConfigSetMultisigCreationFeeInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Program<wbr/>Config<wbr/>Set<wbr/>Multisig<wbr/>Creation<wbr/>Fee<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.ProgramConfigSetMultisigCreationFeeInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Program<wbr/>Config<wbr/>Set<wbr/>Multisig<wbr/>Creation<wbr/>Fee<wbr/>Instruction<wbr/>Args</span></a>
<a href="../types/generated.ProgramConfigSetTreasuryInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Program<wbr/>Config<wbr/>Set<wbr/>Treasury<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.ProgramConfigSetTreasuryInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Program<wbr/>Config<wbr/>Set<wbr/>Treasury<wbr/>Instruction<wbr/>Args</span></a>
<a href="../types/generated.ProposalActivateInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Proposal<wbr/>Activate<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.ProposalApproveInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Proposal<wbr/>Approve<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.ProposalApproveInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Proposal<wbr/>Approve<wbr/>Instruction<wbr/>Args</span></a>
<a href="../types/generated.ProposalArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Proposal<wbr/>Args</span></a>
<a href="../types/generated.ProposalCancelInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Proposal<wbr/>Cancel<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.ProposalCancelInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Proposal<wbr/>Cancel<wbr/>Instruction<wbr/>Args</span></a>
<a href="../types/generated.ProposalCreateInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Proposal<wbr/>Create<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.ProposalCreateInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Proposal<wbr/>Create<wbr/>Instruction<wbr/>Args</span></a>
<a href="../types/generated.ProposalRejectInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Proposal<wbr/>Reject<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.ProposalRejectInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Proposal<wbr/>Reject<wbr/>Instruction<wbr/>Args</span></a>
<a href="../types/generated.ProposalStatus.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Proposal<wbr/>Status</span></a>
<a href="../types/generated.ProposalStatusRecord.html" class="tsd-index-link tsd-is-private"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Proposal<wbr/>Status<wbr/>Record</span></a>
<a href="../types/generated.SpendingLimitArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Spending<wbr/>Limit<wbr/>Args</span></a>
<a href="../types/generated.SpendingLimitUseInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Spending<wbr/>Limit<wbr/>Use<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.SpendingLimitUseInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Spending<wbr/>Limit<wbr/>Use<wbr/>Instruction<wbr/>Args</span></a>
<a href="../types/generated.VaultBatchTransactionAccountCloseInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Vault<wbr/>Batch<wbr/>Transaction<wbr/>Account<wbr/>Close<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.VaultBatchTransactionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Vault<wbr/>Batch<wbr/>Transaction<wbr/>Args</span></a>
<a href="../types/generated.VaultTransactionAccountsCloseInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Vault<wbr/>Transaction<wbr/>Accounts<wbr/>Close<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.VaultTransactionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Vault<wbr/>Transaction<wbr/>Args</span></a>
<a href="../types/generated.VaultTransactionCreateInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Vault<wbr/>Transaction<wbr/>Create<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../types/generated.VaultTransactionCreateInstructionArgs.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Vault<wbr/>Transaction<wbr/>Create<wbr/>Instruction<wbr/>Args</span></a>
<a href="../types/generated.VaultTransactionExecuteInstructionAccounts.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Vault<wbr/>Transaction<wbr/>Execute<wbr/>Instruction<wbr/>Accounts</span></a>
<a href="../variables/generated.PROGRAM_ADDRESS.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>PROGRAM_<wbr/>ADDRESS</span></a>
<a href="../variables/generated.PROGRAM_ID.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>PROGRAM_<wbr/>ID</span></a>
<a href="../variables/generated.batchAccountsCloseStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>batch<wbr/>Accounts<wbr/>Close<wbr/>Struct</span></a>
<a href="../variables/generated.batchAddTransactionArgsBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>batch<wbr/>Add<wbr/>Transaction<wbr/>Args<wbr/>Beet</span></a>
<a href="../variables/generated.batchAddTransactionStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>batch<wbr/>Add<wbr/>Transaction<wbr/>Struct</span></a>
<a href="../variables/generated.batchBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>batch<wbr/>Beet</span></a>
<a href="../variables/generated.batchCreateArgsBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>batch<wbr/>Create<wbr/>Args<wbr/>Beet</span></a>
<a href="../variables/generated.batchCreateStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>batch<wbr/>Create<wbr/>Struct</span></a>
<a href="../variables/generated.batchExecuteTransactionStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>batch<wbr/>Execute<wbr/>Transaction<wbr/>Struct</span></a>
<a href="../variables/generated.configActionBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>config<wbr/>Action<wbr/>Beet</span></a>
<a href="../variables/generated.configTransactionAccountsCloseStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>config<wbr/>Transaction<wbr/>Accounts<wbr/>Close<wbr/>Struct</span></a>
<a href="../variables/generated.configTransactionBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>config<wbr/>Transaction<wbr/>Beet</span></a>
<a href="../variables/generated.configTransactionCreateArgsBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>config<wbr/>Transaction<wbr/>Create<wbr/>Args<wbr/>Beet</span></a>
<a href="../variables/generated.configTransactionCreateStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>config<wbr/>Transaction<wbr/>Create<wbr/>Struct</span></a>
<a href="../variables/generated.configTransactionExecuteStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>config<wbr/>Transaction<wbr/>Execute<wbr/>Struct</span></a>
<a href="../variables/generated.memberBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>member<wbr/>Beet</span></a>
<a href="../variables/generated.multisigAddMemberArgsBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Add<wbr/>Member<wbr/>Args<wbr/>Beet</span></a>
<a href="../variables/generated.multisigAddMemberStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Add<wbr/>Member<wbr/>Struct</span></a>
<a href="../variables/generated.multisigAddSpendingLimitArgsBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Add<wbr/>Spending<wbr/>Limit<wbr/>Args<wbr/>Beet</span></a>
<a href="../variables/generated.multisigAddSpendingLimitStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Add<wbr/>Spending<wbr/>Limit<wbr/>Struct</span></a>
<a href="../variables/generated.multisigBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Beet</span></a>
<a href="../variables/generated.multisigChangeThresholdArgsBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Change<wbr/>Threshold<wbr/>Args<wbr/>Beet</span></a>
<a href="../variables/generated.multisigChangeThresholdStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Change<wbr/>Threshold<wbr/>Struct</span></a>
<a href="../variables/generated.multisigCompiledInstructionBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Compiled<wbr/>Instruction<wbr/>Beet</span></a>
<a href="../variables/generated.multisigCreateArgsBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Create<wbr/>Args<wbr/>Beet</span></a>
<a href="../variables/generated.multisigCreateArgsV2Beet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Create<wbr/>Args<wbr/>V2<wbr/>Beet</span></a>
<a href="../variables/generated.multisigCreateStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Create<wbr/>Struct</span></a>
<a href="../variables/generated.multisigCreateV2Struct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Create<wbr/>V2<wbr/>Struct</span></a>
<a href="../variables/generated.multisigMessageAddressTableLookupBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Message<wbr/>Address<wbr/>Table<wbr/>Lookup<wbr/>Beet</span></a>
<a href="../variables/generated.multisigRemoveMemberArgsBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Remove<wbr/>Member<wbr/>Args<wbr/>Beet</span></a>
<a href="../variables/generated.multisigRemoveMemberStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Remove<wbr/>Member<wbr/>Struct</span></a>
<a href="../variables/generated.multisigRemoveSpendingLimitArgsBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Remove<wbr/>Spending<wbr/>Limit<wbr/>Args<wbr/>Beet</span></a>
<a href="../variables/generated.multisigRemoveSpendingLimitStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Remove<wbr/>Spending<wbr/>Limit<wbr/>Struct</span></a>
<a href="../variables/generated.multisigSetConfigAuthorityArgsBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Set<wbr/>Config<wbr/>Authority<wbr/>Args<wbr/>Beet</span></a>
<a href="../variables/generated.multisigSetConfigAuthorityStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Set<wbr/>Config<wbr/>Authority<wbr/>Struct</span></a>
<a href="../variables/generated.multisigSetRentCollectorArgsBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Set<wbr/>Rent<wbr/>Collector<wbr/>Args<wbr/>Beet</span></a>
<a href="../variables/generated.multisigSetRentCollectorStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Set<wbr/>Rent<wbr/>Collector<wbr/>Struct</span></a>
<a href="../variables/generated.multisigSetTimeLockArgsBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Set<wbr/>Time<wbr/>Lock<wbr/>Args<wbr/>Beet</span></a>
<a href="../variables/generated.multisigSetTimeLockStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Set<wbr/>Time<wbr/>Lock<wbr/>Struct</span></a>
<a href="../variables/generated.periodBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>period<wbr/>Beet</span></a>
<a href="../variables/generated.permissionsBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>permissions<wbr/>Beet</span></a>
<a href="../variables/generated.programConfigBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>program<wbr/>Config<wbr/>Beet</span></a>
<a href="../variables/generated.programConfigInitArgsBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>program<wbr/>Config<wbr/>Init<wbr/>Args<wbr/>Beet</span></a>
<a href="../variables/generated.programConfigInitStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>program<wbr/>Config<wbr/>Init<wbr/>Struct</span></a>
<a href="../variables/generated.programConfigSetAuthorityArgsBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>program<wbr/>Config<wbr/>Set<wbr/>Authority<wbr/>Args<wbr/>Beet</span></a>
<a href="../variables/generated.programConfigSetAuthorityStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>program<wbr/>Config<wbr/>Set<wbr/>Authority<wbr/>Struct</span></a>
<a href="../variables/generated.programConfigSetMultisigCreationFeeArgsBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>program<wbr/>Config<wbr/>Set<wbr/>Multisig<wbr/>Creation<wbr/>Fee<wbr/>Args<wbr/>Beet</span></a>
<a href="../variables/generated.programConfigSetMultisigCreationFeeStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>program<wbr/>Config<wbr/>Set<wbr/>Multisig<wbr/>Creation<wbr/>Fee<wbr/>Struct</span></a>
<a href="../variables/generated.programConfigSetTreasuryArgsBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>program<wbr/>Config<wbr/>Set<wbr/>Treasury<wbr/>Args<wbr/>Beet</span></a>
<a href="../variables/generated.programConfigSetTreasuryStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>program<wbr/>Config<wbr/>Set<wbr/>Treasury<wbr/>Struct</span></a>
<a href="../variables/generated.proposalActivateStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>proposal<wbr/>Activate<wbr/>Struct</span></a>
<a href="../variables/generated.proposalApproveStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>proposal<wbr/>Approve<wbr/>Struct</span></a>
<a href="../variables/generated.proposalBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>proposal<wbr/>Beet</span></a>
<a href="../variables/generated.proposalCancelStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>proposal<wbr/>Cancel<wbr/>Struct</span></a>
<a href="../variables/generated.proposalCreateArgsBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>proposal<wbr/>Create<wbr/>Args<wbr/>Beet</span></a>
<a href="../variables/generated.proposalCreateStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>proposal<wbr/>Create<wbr/>Struct</span></a>
<a href="../variables/generated.proposalRejectStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>proposal<wbr/>Reject<wbr/>Struct</span></a>
<a href="../variables/generated.proposalStatusBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>proposal<wbr/>Status<wbr/>Beet</span></a>
<a href="../variables/generated.proposalVoteArgsBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>proposal<wbr/>Vote<wbr/>Args<wbr/>Beet</span></a>
<a href="../variables/generated.spendingLimitBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>spending<wbr/>Limit<wbr/>Beet</span></a>
<a href="../variables/generated.spendingLimitUseArgsBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>spending<wbr/>Limit<wbr/>Use<wbr/>Args<wbr/>Beet</span></a>
<a href="../variables/generated.spendingLimitUseStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>spending<wbr/>Limit<wbr/>Use<wbr/>Struct</span></a>
<a href="../variables/generated.vaultBatchTransactionAccountCloseStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>vault<wbr/>Batch<wbr/>Transaction<wbr/>Account<wbr/>Close<wbr/>Struct</span></a>
<a href="../variables/generated.vaultBatchTransactionBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>vault<wbr/>Batch<wbr/>Transaction<wbr/>Beet</span></a>
<a href="../variables/generated.vaultTransactionAccountsCloseStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>vault<wbr/>Transaction<wbr/>Accounts<wbr/>Close<wbr/>Struct</span></a>
<a href="../variables/generated.vaultTransactionBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>vault<wbr/>Transaction<wbr/>Beet</span></a>
<a href="../variables/generated.vaultTransactionCreateArgsBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>vault<wbr/>Transaction<wbr/>Create<wbr/>Args<wbr/>Beet</span></a>
<a href="../variables/generated.vaultTransactionCreateStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>vault<wbr/>Transaction<wbr/>Create<wbr/>Struct</span></a>
<a href="../variables/generated.vaultTransactionExecuteStruct.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>vault<wbr/>Transaction<wbr/>Execute<wbr/>Struct</span></a>
<a href="../variables/generated.vaultTransactionMessageBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>vault<wbr/>Transaction<wbr/>Message<wbr/>Beet</span></a>
<a href="../variables/generated.voteBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>vote<wbr/>Beet</span></a>
<a href="../functions/generated.createBatchAccountsCloseInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Batch<wbr/>Accounts<wbr/>Close<wbr/>Instruction</span></a>
<a href="../functions/generated.createBatchAddTransactionInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Batch<wbr/>Add<wbr/>Transaction<wbr/>Instruction</span></a>
<a href="../functions/generated.createBatchCreateInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Batch<wbr/>Create<wbr/>Instruction</span></a>
<a href="../functions/generated.createBatchExecuteTransactionInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Batch<wbr/>Execute<wbr/>Transaction<wbr/>Instruction</span></a>
<a href="../functions/generated.createConfigTransactionAccountsCloseInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Config<wbr/>Transaction<wbr/>Accounts<wbr/>Close<wbr/>Instruction</span></a>
<a href="../functions/generated.createConfigTransactionCreateInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Config<wbr/>Transaction<wbr/>Create<wbr/>Instruction</span></a>
<a href="../functions/generated.createConfigTransactionExecuteInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Config<wbr/>Transaction<wbr/>Execute<wbr/>Instruction</span></a>
<a href="../functions/generated.createMultisigAddMemberInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Multisig<wbr/>Add<wbr/>Member<wbr/>Instruction</span></a>
<a href="../functions/generated.createMultisigAddSpendingLimitInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Multisig<wbr/>Add<wbr/>Spending<wbr/>Limit<wbr/>Instruction</span></a>
<a href="../functions/generated.createMultisigChangeThresholdInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Multisig<wbr/>Change<wbr/>Threshold<wbr/>Instruction</span></a>
<a href="../functions/generated.createMultisigCreateInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Multisig<wbr/>Create<wbr/>Instruction</span></a>
<a href="../functions/generated.createMultisigCreateV2Instruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Multisig<wbr/>Create<wbr/>V2<wbr/>Instruction</span></a>
<a href="../functions/generated.createMultisigRemoveMemberInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Multisig<wbr/>Remove<wbr/>Member<wbr/>Instruction</span></a>
<a href="../functions/generated.createMultisigRemoveSpendingLimitInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Multisig<wbr/>Remove<wbr/>Spending<wbr/>Limit<wbr/>Instruction</span></a>
<a href="../functions/generated.createMultisigSetConfigAuthorityInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Multisig<wbr/>Set<wbr/>Config<wbr/>Authority<wbr/>Instruction</span></a>
<a href="../functions/generated.createMultisigSetRentCollectorInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Multisig<wbr/>Set<wbr/>Rent<wbr/>Collector<wbr/>Instruction</span></a>
<a href="../functions/generated.createMultisigSetTimeLockInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Multisig<wbr/>Set<wbr/>Time<wbr/>Lock<wbr/>Instruction</span></a>
<a href="../functions/generated.createProgramConfigInitInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Program<wbr/>Config<wbr/>Init<wbr/>Instruction</span></a>
<a href="../functions/generated.createProgramConfigSetAuthorityInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Program<wbr/>Config<wbr/>Set<wbr/>Authority<wbr/>Instruction</span></a>
<a href="../functions/generated.createProgramConfigSetMultisigCreationFeeInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Program<wbr/>Config<wbr/>Set<wbr/>Multisig<wbr/>Creation<wbr/>Fee<wbr/>Instruction</span></a>
<a href="../functions/generated.createProgramConfigSetTreasuryInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Program<wbr/>Config<wbr/>Set<wbr/>Treasury<wbr/>Instruction</span></a>
<a href="../functions/generated.createProposalActivateInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Proposal<wbr/>Activate<wbr/>Instruction</span></a>
<a href="../functions/generated.createProposalApproveInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Proposal<wbr/>Approve<wbr/>Instruction</span></a>
<a href="../functions/generated.createProposalCancelInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Proposal<wbr/>Cancel<wbr/>Instruction</span></a>
<a href="../functions/generated.createProposalCreateInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Proposal<wbr/>Create<wbr/>Instruction</span></a>
<a href="../functions/generated.createProposalRejectInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Proposal<wbr/>Reject<wbr/>Instruction</span></a>
<a href="../functions/generated.createSpendingLimitUseInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Spending<wbr/>Limit<wbr/>Use<wbr/>Instruction</span></a>
<a href="../functions/generated.createVaultBatchTransactionAccountCloseInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Vault<wbr/>Batch<wbr/>Transaction<wbr/>Account<wbr/>Close<wbr/>Instruction</span></a>
<a href="../functions/generated.createVaultTransactionAccountsCloseInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Vault<wbr/>Transaction<wbr/>Accounts<wbr/>Close<wbr/>Instruction</span></a>
<a href="../functions/generated.createVaultTransactionCreateInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Vault<wbr/>Transaction<wbr/>Create<wbr/>Instruction</span></a>
<a href="../functions/generated.createVaultTransactionExecuteInstruction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>create<wbr/>Vault<wbr/>Transaction<wbr/>Execute<wbr/>Instruction</span></a>
<a href="../functions/generated.errorFromCode.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>error<wbr/>From<wbr/>Code</span></a>
<a href="../functions/generated.errorFromName.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>error<wbr/>From<wbr/>Name</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">userTypes</h3><div class="tsd-index-list"><a href="../types/generated.ConfigAction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Config<wbr/>Action</span></a>
<a href="../types/generated.ConfigActionRecord.html" class="tsd-index-link tsd-is-private"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Config<wbr/>Action<wbr/>Record</span></a>
<a href="../types/generated.ProposalStatus.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Proposal<wbr/>Status</span></a>
<a href="../types/generated.ProposalStatusRecord.html" class="tsd-index-link tsd-is-private"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-2097152"></use></svg><span>Proposal<wbr/>Status<wbr/>Record</span></a>
<a href="../variables/generated.batchAddTransactionArgsBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>batch<wbr/>Add<wbr/>Transaction<wbr/>Args<wbr/>Beet</span></a>
<a href="../variables/generated.batchCreateArgsBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>batch<wbr/>Create<wbr/>Args<wbr/>Beet</span></a>
<a href="../variables/generated.configActionBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>config<wbr/>Action<wbr/>Beet</span></a>
<a href="../variables/generated.configTransactionCreateArgsBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>config<wbr/>Transaction<wbr/>Create<wbr/>Args<wbr/>Beet</span></a>
<a href="../variables/generated.memberBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>member<wbr/>Beet</span></a>
<a href="../variables/generated.multisigAddMemberArgsBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Add<wbr/>Member<wbr/>Args<wbr/>Beet</span></a>
<a href="../variables/generated.multisigAddSpendingLimitArgsBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Add<wbr/>Spending<wbr/>Limit<wbr/>Args<wbr/>Beet</span></a>
<a href="../variables/generated.multisigChangeThresholdArgsBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Change<wbr/>Threshold<wbr/>Args<wbr/>Beet</span></a>
<a href="../variables/generated.multisigCompiledInstructionBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Compiled<wbr/>Instruction<wbr/>Beet</span></a>
<a href="../variables/generated.multisigCreateArgsBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Create<wbr/>Args<wbr/>Beet</span></a>
<a href="../variables/generated.multisigCreateArgsV2Beet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Create<wbr/>Args<wbr/>V2<wbr/>Beet</span></a>
<a href="../variables/generated.multisigMessageAddressTableLookupBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Message<wbr/>Address<wbr/>Table<wbr/>Lookup<wbr/>Beet</span></a>
<a href="../variables/generated.multisigRemoveMemberArgsBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Remove<wbr/>Member<wbr/>Args<wbr/>Beet</span></a>
<a href="../variables/generated.multisigRemoveSpendingLimitArgsBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Remove<wbr/>Spending<wbr/>Limit<wbr/>Args<wbr/>Beet</span></a>
<a href="../variables/generated.multisigSetConfigAuthorityArgsBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Set<wbr/>Config<wbr/>Authority<wbr/>Args<wbr/>Beet</span></a>
<a href="../variables/generated.multisigSetRentCollectorArgsBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Set<wbr/>Rent<wbr/>Collector<wbr/>Args<wbr/>Beet</span></a>
<a href="../variables/generated.multisigSetTimeLockArgsBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>multisig<wbr/>Set<wbr/>Time<wbr/>Lock<wbr/>Args<wbr/>Beet</span></a>
<a href="../variables/generated.periodBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>period<wbr/>Beet</span></a>
<a href="../variables/generated.permissionsBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>permissions<wbr/>Beet</span></a>
<a href="../variables/generated.programConfigInitArgsBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>program<wbr/>Config<wbr/>Init<wbr/>Args<wbr/>Beet</span></a>
<a href="../variables/generated.programConfigSetAuthorityArgsBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>program<wbr/>Config<wbr/>Set<wbr/>Authority<wbr/>Args<wbr/>Beet</span></a>
<a href="../variables/generated.programConfigSetMultisigCreationFeeArgsBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>program<wbr/>Config<wbr/>Set<wbr/>Multisig<wbr/>Creation<wbr/>Fee<wbr/>Args<wbr/>Beet</span></a>
<a href="../variables/generated.programConfigSetTreasuryArgsBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>program<wbr/>Config<wbr/>Set<wbr/>Treasury<wbr/>Args<wbr/>Beet</span></a>
<a href="../variables/generated.proposalCreateArgsBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>proposal<wbr/>Create<wbr/>Args<wbr/>Beet</span></a>
<a href="../variables/generated.proposalStatusBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>proposal<wbr/>Status<wbr/>Beet</span></a>
<a href="../variables/generated.proposalVoteArgsBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>proposal<wbr/>Vote<wbr/>Args<wbr/>Beet</span></a>
<a href="../variables/generated.spendingLimitUseArgsBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>spending<wbr/>Limit<wbr/>Use<wbr/>Args<wbr/>Beet</span></a>
<a href="../variables/generated.vaultTransactionCreateArgsBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>vault<wbr/>Transaction<wbr/>Create<wbr/>Args<wbr/>Beet</span></a>
<a href="../variables/generated.vaultTransactionMessageBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>vault<wbr/>Transaction<wbr/>Message<wbr/>Beet</span></a>
<a href="../variables/generated.voteBeet.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>vote<wbr/>Beet</span></a>
</div></section></section></section></div><div class="col-sidebar"><div class="page-menu"><div class="tsd-navigation settings"><details class="tsd-index-accordion"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><use href="#icon-chevronDown"></use></svg>Settings</h3></summary><div class="tsd-accordion-details"><div class="tsd-filter-visibility"><h4 class="uppercase">Member Visibility</h4><form><ul id="tsd-filter-options"><li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-protected" name="protected"/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>Protected</span></label></li><li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-private" name="private"/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>Private</span></label></li><li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-inherited" name="inherited" checked/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>Inherited</span></label></li><li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-external" name="external"/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>External</span></label></li></ul></form></div><div class="tsd-theme-toggle"><h4 class="uppercase">Theme</h4><select id="tsd-theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></div></div></details></div></div><div class="site-menu"><nav class="tsd-navigation"><a href="../modules.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-1"></use></svg><span>@sqds/multisig</span></a><ul class="tsd-small-nested-navigation" id="tsd-nav-container" data-base=".."><li><a href="../modules.html#PROGRAM_ADDRESS"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4194304"></use></svg>PROGRAM_ADDRESS</a></li><li><a href="../modules.html#PROGRAM_ID"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4194304"></use></svg>PROGRAM_ID</a></li><li><a href="accounts.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4"></use></svg>accounts</a></li><li><a href="errors.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4"></use></svg>errors</a></li><li><a href="generated.html" class="current"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4"></use></svg>generated</a></li><li><a href="instructions.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4"></use></svg>instructions</a></li><li><a href="rpc.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4"></use></svg>rpc</a></li><li><a href="transactions.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4"></use></svg>transactions</a></li><li><a href="types.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4"></use></svg>types</a></li><li><a href="utils.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4"></use></svg>utils</a></li><li><a href="../functions/getBatchTransactionPda.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg>getBatchTransactionPda</a></li><li><a href="../functions/getEphemeralSignerPda.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg>getEphemeralSignerPda</a></li><li><a href="../functions/getMultisigPda.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg>getMultisigPda</a></li><li><a href="../functions/getProgramConfigPda.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg>getProgramConfigPda</a></li><li><a href="../functions/getProposalPda.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg>getProposalPda</a></li><li><a href="../functions/getSpendingLimitPda.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg>getSpendingLimitPda</a></li><li><a href="../functions/getTransactionPda.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg>getTransactionPda</a></li><li><a href="../functions/getVaultPda.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg>getVaultPda</a></li></ul></nav></div></div></div><div class="tsd-generator"><p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></div><div class="overlay"></div><svg style="display: none"><g id="icon-1"><rect fill="var(--color-icon-background)" stroke="var(--color-ts-module)" stroke-width="1.5" x="1" y="1" width="22" height="22" rx="6"></rect><path d="M9.162 16V7.24H10.578L11.514 10.072C11.602 10.328 11.674 10.584 11.73 10.84C11.794 11.088 11.842 11.28 11.874 11.416C11.906 11.28 11.954 11.088 12.018 10.84C12.082 10.584 12.154 10.324 12.234 10.06L13.122 7.24H14.538V16H13.482V12.82C13.482 12.468 13.49 12.068 13.506 11.62C13.53 11.172 13.558 10.716 13.59 10.252C13.622 9.78 13.654 9.332 13.686 8.908C13.726 8.476 13.762 8.1 13.794 7.78L12.366 12.16H11.334L9.894 7.78C9.934 8.092 9.97 8.456 10.002 8.872C10.042 9.28 10.078 9.716 10.11 10.18C10.142 10.636 10.166 11.092 10.182 11.548C10.206 12.004 10.218 12.428 10.218 12.82V16H9.162Z" fill="var(--color-text)"></path></g><g id="icon-2"><rect fill="var(--color-icon-background)" stroke="var(--color-ts-module)" stroke-width="1.5" x="1" y="1" width="22" height="22" rx="6"></rect><path d="M9.162 16V7.24H10.578L11.514 10.072C11.602 10.328 11.674 10.584 11.73 10.84C11.794 11.088 11.842 11.28 11.874 11.416C11.906 11.28 11.954 11.088 12.018 10.84C12.082 10.584 12.154 10.324 12.234 10.06L13.122 7.24H14.538V16H13.482V12.82C13.482 12.468 13.49 12.068 13.506 11.62C13.53 11.172 13.558 10.716 13.59 10.252C13.622 9.78 13.654 9.332 13.686 8.908C13.726 8.476 13.762 8.1 13.794 7.78L12.366 12.16H11.334L9.894 7.78C9.934 8.092 9.97 8.456 10.002 8.872C10.042 9.28 10.078 9.716 10.11 10.18C10.142 10.636 10.166 11.092 10.182 11.548C10.206 12.004 10.218 12.428 10.218 12.82V16H9.162Z" fill="var(--color-text)"></path></g><g id="icon-4"><rect fill="var(--color-icon-background)" stroke="var(--color-ts-namespace)" stroke-width="1.5" x="1" y="1" width="22" height="22" rx="6"></rect><path d="M9.33 16V7.24H10.77L13.446 14.74C13.43 14.54 13.41 14.296 13.386 14.008C13.37 13.712 13.354 13.404 13.338 13.084C13.33 12.756 13.326 12.448 13.326 12.16V7.24H14.37V16H12.93L10.266 8.5C10.282 8.692 10.298 8.936 10.314 9.232C10.33 9.52 10.342 9.828 10.35 10.156C10.366 10.476 10.374 10.784 10.374 11.08V16H9.33Z" fill="var(--color-text)"></path></g><g id="icon-8"><rect fill="var(--color-icon-background)" stroke="var(--color-ts-enum)" stroke-width="1.5" x="1" y="1" width="22" height="22" rx="6"></rect><path d="M9.45 16V7.24H14.49V8.224H10.518V10.936H14.07V11.908H10.518V15.016H14.49V16H9.45Z" fill="var(--color-text)"></path></g><g id="icon-16"><rect fill="var(--color-icon-background)" stroke="#FF984D" stroke-width="1.5" x="1" y="1" width="22" height="22" rx="12"></rect><path d="M9.354 16V7.24H12.174C12.99 7.24 13.638 7.476 14.118 7.948C14.606 8.412 14.85 9.036 14.85 9.82C14.85 10.604 14.606 11.232 14.118 11.704C13.638 12.168 12.99 12.4 12.174 12.4H10.434V16H9.354ZM10.434 11.428H12.174C12.646 11.428 13.022 11.284 13.302 10.996C13.59 10.7 13.734 10.308 13.734 9.82C13.734 9.324 13.59 8.932 13.302 8.644C13.022 8.356 12.646 8.212 12.174 8.212H10.434V11.428Z" fill="var(--color-text)"></path></g><g id="icon-32"><rect fill="var(--color-icon-background)" stroke="var(--color-ts-variable)" stroke-width="1.5" x="1" y="1" width="22" height="22" rx="6"></rect><path d="M11.106 16L8.85 7.24H9.966L11.454 13.192C11.558 13.608 11.646 13.996 11.718 14.356C11.79 14.708 11.842 14.976 11.874 15.16C11.906 14.976 11.954 14.708 12.018 14.356C12.09 13.996 12.178 13.608 12.282 13.192L13.758 7.24H14.85L12.582 16H11.106Z" fill="var(--color-text)"></path></g><g id="icon-64"><rect fill="var(--color-icon-background)" stroke="var(--color-ts-function)" stroke-width="1.5" x="1" y="1" width="22" height="22" rx="6"></rect><path d="M9.39 16V7.24H14.55V8.224H10.446V11.128H14.238V12.112H10.47V16H9.39Z" fill="var(--color-text)"></path></g><g id="icon-128"><rect fill="var(--color-icon-background)" stroke="var(--color-ts-class)" stroke-width="1.5" x="1" y="1" width="22" height="22" rx="6"></rect><path d="M11.898 16.1201C11.098 16.1201 10.466 15.8961 10.002 15.4481C9.53803 15.0001 9.30603 14.3841 9.30603 13.6001V9.64012C9.30603 8.85612 9.53803 8.24012 10.002 7.79212C10.466 7.34412 11.098 7.12012 11.898 7.12012C12.682 7.12012 13.306 7.34812 13.77 7.80412C14.234 8.25212 14.466 8.86412 14.466 9.64012H13.386C13.386 9.14412 13.254 8.76412 12.99 8.50012C12.734 8.22812 12.37 8.09212 11.898 8.09212C11.426 8.09212 11.054 8.22412 10.782 8.48812C10.518 8.75212 10.386 9.13212 10.386 9.62812V13.6001C10.386 14.0961 10.518 14.4801 10.782 14.7521C11.054 15.0161 11.426 15.1481 11.898 15.1481C12.37 15.1481 12.734 15.0161 12.99 14.7521C13.254 14.4801 13.386 14.0961 13.386 13.6001H14.466C14.466 14.3761 14.234 14.9921 13.77 15.4481C13.306 15.8961 12.682 16.1201 11.898 16.1201Z" fill="var(--color-text)"></path></g><g id="icon-256"><rect fill="var(--color-icon-background)" stroke="var(--color-ts-interface)" stroke-width="1.5" x="1" y="1" width="22" height="22" rx="6"></rect><path d="M9.51 16V15.016H11.298V8.224H9.51V7.24H14.19V8.224H12.402V15.016H14.19V16H9.51Z" fill="var(--color-text)"></path></g><g id="icon-512"><rect fill="var(--color-icon-background)" stroke="#4D7FFF" stroke-width="1.5" x="1" y="1" width="22" height="22" rx="12"></rect><path d="M11.898 16.1201C11.098 16.1201 10.466 15.8961 10.002 15.4481C9.53803 15.0001 9.30603 14.3841 9.30603 13.6001V9.64012C9.30603 8.85612 9.53803 8.24012 10.002 7.79212C10.466 7.34412 11.098 7.12012 11.898 7.12012C12.682 7.12012 13.306 7.34812 13.77 7.80412C14.234 8.25212 14.466 8.86412 14.466 9.64012H13.386C13.386 9.14412 13.254 8.76412 12.99 8.50012C12.734 8.22812 12.37 8.09212 11.898 8.09212C11.426 8.09212 11.054 8.22412 10.782 8.48812C10.518 8.75212 10.386 9.13212 10.386 9.62812V13.6001C10.386 14.0961 10.518 14.4801 10.782 14.7521C11.054 15.0161 11.426 15.1481 11.898 15.1481C12.37 15.1481 12.734 15.0161 12.99 14.7521C13.254 14.4801 13.386 14.0961 13.386 13.6001H14.466C14.466 14.3761 14.234 14.9921 13.77 15.4481C13.306 15.8961 12.682 16.1201 11.898 16.1201Z" fill="var(--color-text)"></path></g><g id="icon-1024"><rect fill="var(--color-icon-background)" stroke="#FF984D" stroke-width="1.5" x="1" y="1" width="22" height="22" rx="12"></rect><path d="M9.354 16V7.24H12.174C12.99 7.24 13.638 7.476 14.118 7.948C14.606 8.412 14.85 9.036 14.85 9.82C14.85 10.604 14.606 11.232 14.118 11.704C13.638 12.168 12.99 12.4 12.174 12.4H10.434V16H9.354ZM10.434 11.428H12.174C12.646 11.428 13.022 11.284 13.302 10.996C13.59 10.7 13.734 10.308 13.734 9.82C13.734 9.324 13.59 8.932 13.302 8.644C13.022 8.356 12.646 8.212 12.174 8.212H10.434V11.428Z" fill="var(--color-text)"></path></g><g id="icon-2048"><rect fill="var(--color-icon-background)" stroke="#FF4DB8" stroke-width="1.5" x="1" y="1" width="22" height="22" rx="12"></rect><path d="M9.162 16V7.24H10.578L11.514 10.072C11.602 10.328 11.674 10.584 11.73 10.84C11.794 11.088 11.842 11.28 11.874 11.416C11.906 11.28 11.954 11.088 12.018 10.84C12.082 10.584 12.154 10.324 12.234 10.06L13.122 7.24H14.538V16H13.482V12.82C13.482 12.468 13.49 12.068 13.506 11.62C13.53 11.172 13.558 10.716 13.59 10.252C13.622 9.78 13.654 9.332 13.686 8.908C13.726 8.476 13.762 8.1 13.794 7.78L12.366 12.16H11.334L9.894 7.78C9.934 8.092 9.97 8.456 10.002 8.872C10.042 9.28 10.078 9.716 10.11 10.18C10.142 10.636 10.166 11.092 10.182 11.548C10.206 12.004 10.218 12.428 10.218 12.82V16H9.162Z" fill="var(--color-text)"></path></g><g id="icon-4096"><rect fill="var(--color-icon-background)" stroke="var(--color-ts-function)" stroke-width="1.5" x="1" y="1" width="22" height="22" rx="6"></rect><path d="M9.39 16V7.24H14.55V8.224H10.446V11.128H14.238V12.112H10.47V16H9.39Z" fill="var(--color-text)"></path></g><g id="icon-8192"><rect fill="var(--color-icon-background)" stroke="#FF984D" stroke-width="1.5" x="1" y="1" width="22" height="22" rx="12"></rect><path d="M9.354 16V7.24H12.174C12.99 7.24 13.638 7.476 14.118 7.948C14.606 8.412 14.85 9.036 14.85 9.82C14.85 10.604 14.606 11.232 14.118 11.704C13.638 12.168 12.99 12.4 12.174 12.4H10.434V16H9.354ZM10.434 11.428H12.174C12.646 11.428 13.022 11.284 13.302 10.996C13.59 10.7 13.734 10.308 13.734 9.82C13.734 9.324 13.59 8.932 13.302 8.644C13.022 8.356 12.646 8.212 12.174 8.212H10.434V11.428Z" fill="var(--color-text)"></path></g><g id="icon-16384"><rect fill="var(--color-icon-background)" stroke="#4D7FFF" stroke-width="1.5" x="1" y="1" width="22" height="22" rx="12"></rect><path d="M11.898 16.1201C11.098 16.1201 10.466 15.8961 10.002 15.4481C9.53803 15.0001 9.30603 14.3841 9.30603 13.6001V9.64012C9.30603 8.85612 9.53803 8.24012 10.002 7.79212C10.466 7.34412 11.098 7.12012 11.898 7.12012C12.682 7.12012 13.306 7.34812 13.77 7.80412C14.234 8.25212 14.466 8.86412 14.466 9.64012H13.386C13.386 9.14412 13.254 8.76412 12.99 8.50012C12.734 8.22812 12.37 8.09212 11.898 8.09212C11.426 8.09212 11.054 8.22412 10.782 8.48812C10.518 8.75212 10.386 9.13212 10.386 9.62812V13.6001C10.386 14.0961 10.518 14.4801 10.782 14.7521C11.054 15.0161 11.426 15.1481 11.898 15.1481C12.37 15.1481 12.734 15.0161 12.99 14.7521C13.254 14.4801 13.386 14.0961 13.386 13.6001H14.466C14.466 14.3761 14.234 14.9921 13.77 15.4481C13.306 15.8961 12.682 16.1201 11.898 16.1201Z" fill="var(--color-text)"></path></g><g id="icon-32768"><rect fill="var(--color-icon-background)" stroke="#FF984D" stroke-width="1.5" x="1" y="1" width="22" height="22" rx="12"></rect><path d="M9.354 16V7.24H12.174C12.99 7.24 13.638 7.476 14.118 7.948C14.606 8.412 14.85 9.036 14.85 9.82C14.85 10.604 14.606 11.232 14.118 11.704C13.638 12.168 12.99 12.4 12.174 12.4H10.434V16H9.354ZM10.434 11.428H12.174C12.646 11.428 13.022 11.284 13.302 10.996C13.59 10.7 13.734 10.308 13.734 9.82C13.734 9.324 13.59 8.932 13.302 8.644C13.022 8.356 12.646 8.212 12.174 8.212H10.434V11.428Z" fill="var(--color-text)"></path></g><g id="icon-65536"><rect fill="var(--color-icon-background)" stroke="var(--color-ts-type-alias)" stroke-width="1.5" x="1" y="1" width="22" height="22" rx="6"></rect><path d="M11.31 16V8.224H8.91V7.24H14.79V8.224H12.39V16H11.31Z" fill="var(--color-text)"></path></g><g id="icon-131072"><rect fill="var(--color-icon-background)" stroke="var(--color-ts-type-alias)" stroke-width="1.5" x="1" y="1" width="22" height="22" rx="6"></rect><path d="M11.31 16V8.224H8.91V7.24H14.79V8.224H12.39V16H11.31Z" fill="var(--color-text)"></path></g><g id="icon-262144"><rect fill="var(--color-icon-background)" stroke="#FF4D4D" stroke-width="1.5" x="1" y="1" width="22" height="22" rx="12"></rect><path d="M8.85 16L11.13 7.24H12.582L14.85 16H13.758L13.182 13.672H10.53L9.954 16H8.85ZM10.746 12.76H12.954L12.282 10.06C12.154 9.548 12.054 9.12 11.982 8.776C11.91 8.432 11.866 8.208 11.85 8.104C11.834 8.208 11.79 8.432 11.718 8.776C11.646 9.12 11.546 9.544 11.418 10.048L10.746 12.76Z" fill="var(--color-text)"></path></g><g id="icon-524288"><rect fill="var(--color-icon-background)" stroke="#FF4D4D" stroke-width="1.5" x="1" y="1" width="22" height="22" rx="12"></rect><path d="M8.85 16L11.13 7.24H12.582L14.85 16H13.758L13.182 13.672H10.53L9.954 16H8.85ZM10.746 12.76H12.954L12.282 10.06C12.154 9.548 12.054 9.12 11.982 8.776C11.91 8.432 11.866 8.208 11.85 8.104C11.834 8.208 11.79 8.432 11.718 8.776C11.646 9.12 11.546 9.544 11.418 10.048L10.746 12.76Z" fill="var(--color-text)"></path></g><g id="icon-1048576"><rect fill="var(--color-icon-background)" stroke="#FF4D4D" stroke-width="1.5" x="1" y="1" width="22" height="22" rx="12"></rect><path d="M8.85 16L11.13 7.24H12.582L14.85 16H13.758L13.182 13.672H10.53L9.954 16H8.85ZM10.746 12.76H12.954L12.282 10.06C12.154 9.548 12.054 9.12 11.982 8.776C11.91 8.432 11.866 8.208 11.85 8.104C11.834 8.208 11.79 8.432 11.718 8.776C11.646 9.12 11.546 9.544 11.418 10.048L10.746 12.76Z" fill="var(--color-text)"></path></g><g id="icon-2097152"><rect fill="var(--color-icon-background)" stroke="var(--color-ts-type-alias)" stroke-width="1.5" x="1" y="1" width="22" height="22" rx="6"></rect><path d="M11.31 16V8.224H8.91V7.24H14.79V8.224H12.39V16H11.31Z" fill="var(--color-text)"></path></g><g id="icon-4194304"><rect fill="var(--color-icon-background)" stroke="#FF4D82" stroke-width="1.5" x="1" y="1" width="22" height="22" rx="12"></rect><path d="M10.354 17V8.24H13.066C13.586 8.24 14.042 8.348 14.434 8.564C14.826 8.772 15.13 9.064 15.346 9.44C15.562 9.816 15.67 10.256 15.67 10.76C15.67 11.352 15.514 11.86 15.202 12.284C14.898 12.708 14.482 13 13.954 13.16L15.79 17H14.518L12.838 13.28H11.434V17H10.354ZM11.434 12.308H13.066C13.514 12.308 13.874 12.168 14.146 11.888C14.418 11.6 14.554 11.224 14.554 10.76C14.554 10.288 14.418 9.912 14.146 9.632C13.874 9.352 13.514 9.212 13.066 9.212H11.434V12.308Z" fill="var(--color-text)"></path></g><g id="icon-chevronDown"><path d="M4.93896 8.531L12 15.591L19.061 8.531L16.939 6.409L12 11.349L7.06098 6.409L4.93896 8.531Z" fill="var(--color-text)"></path></g><g id="icon-chevronSmall"><path d="M1.5 5.50969L8 11.6609L14.5 5.50969L12.5466 3.66086L8 7.96494L3.45341 3.66086L1.5 5.50969Z" fill="var(--color-text)"></path></g><g id="icon-menu"><rect x="1" y="3" width="14" height="2" fill="var(--color-text)"></rect><rect x="1" y="7" width="14" height="2" fill="var(--color-text)"></rect><rect x="1" y="11" width="14" height="2" fill="var(--color-text)"></rect></g><g id="icon-search"><path d="M15.7824 13.833L12.6666 10.7177C12.5259 10.5771 12.3353 10.499 12.1353 10.499H11.6259C12.4884 9.39596 13.001 8.00859 13.001 6.49937C13.001 2.90909 10.0914 0 6.50048 0C2.90959 0 0 2.90909 0 6.49937C0 10.0896 2.90959 12.9987 6.50048 12.9987C8.00996 12.9987 9.39756 12.4863 10.5008 11.6239V12.1332C10.5008 12.3332 10.5789 12.5238 10.7195 12.6644L13.8354 15.7797C14.1292 16.0734 14.6042 16.0734 14.8948 15.7797L15.7793 14.8954C16.0731 14.6017 16.0731 14.1267 15.7824 13.833ZM6.50048 10.499C4.29094 10.499 2.50018 8.71165 2.50018 6.49937C2.50018 4.29021 4.28781 2.49976 6.50048 2.49976C8.71001 2.49976 10.5008 4.28708 10.5008 6.49937C10.5008 8.70852 8.71314 10.499 6.50048 10.499Z" fill="var(--color-text)"></path></g><g id="icon-anchor"><g stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"></path><path d="M10 14a3.5 3.5 0 0 0 5 0l4 -4a3.5 3.5 0 0 0 -5 -5l-.5 .5"></path><path d="M14 10a3.5 3.5 0 0 0 -5 0l-4 4a3.5 3.5 0 0 0 5 5l.5 -.5"></path></g></g></svg></body></html>