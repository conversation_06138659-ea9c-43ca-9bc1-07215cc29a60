import React from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { useWalletModal } from '@solana/wallet-adapter-react-ui';

const ConnectWallet = () => {
  const { publicKey, disconnect, connected } = useWallet();
  const { setVisible } = useWalletModal();

  const handleConnect = () => {
    setVisible(true);
  };

  const handleDisconnect = () => {
    disconnect();
  };

  if (connected && publicKey) {
    return (
      <div>
        <div className="status success">
          ✅ 钱包已连接: {publicKey.toBase58().slice(0, 8)}...{publicKey.toBase58().slice(-8)}
        </div>
        <button className="button" onClick={handleDisconnect}>
          断开连接
        </button>
      </div>
    );
  }

  return (
    <div>
      <div className="status info">
        ℹ️ 请连接 Phantom 钱包以开始使用
      </div>
      <button className="button" onClick={handleConnect}>
        连接钱包
      </button>
    </div>
  );
};

export default ConnectWallet;
