import React, { useState, useEffect } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { useWalletModal } from '@solana/wallet-adapter-react-ui';

const ConnectWallet = () => {
  const { publicKey, disconnect, connected, connect, wallets, select } = useWallet();
  const { setVisible } = useWalletModal();
  const [isConnecting, setIsConnecting] = useState(false);

  // 直接连接 Phantom 钱包
  const connectPhantom = async () => {
    try {
      setIsConnecting(true);

      // 检查是否有 Phantom 钱包
      if (window.solana && window.solana.isPhantom) {
        // 直接使用 window.solana 连接
        const response = await window.solana.connect();
        console.log('Phantom connected:', response.publicKey.toString());
        return;
      }

      // 如果没有直接的 Phantom 对象，使用钱包适配器
      const phantomWallet = wallets.find(wallet =>
        wallet.adapter.name === 'Phantom' ||
        wallet.adapter.name.toLowerCase().includes('phantom')
      );

      if (phantomWallet) {
        select(phantomWallet.adapter.name);
        await connect();
      } else {
        // 使用钱包模态框
        setVisible(true);
      }
    } catch (error) {
      console.error('连接钱包失败:', error);
      // 如果直接连接失败，尝试使用模态框
      setVisible(true);
    } finally {
      setIsConnecting(false);
    }
  };

  const handleDisconnect = async () => {
    try {
      if (window.solana && window.solana.isPhantom) {
        await window.solana.disconnect();
      }
      await disconnect();
    } catch (error) {
      console.error('断开连接失败:', error);
    }
  };

  // 监听 Phantom 钱包状态
  useEffect(() => {
    if (window.solana && window.solana.isPhantom) {
      window.solana.on('connect', () => {
        console.log('Phantom wallet connected');
      });

      window.solana.on('disconnect', () => {
        console.log('Phantom wallet disconnected');
      });
    }
  }, []);

  if (connected && publicKey) {
    return (
      <div>
        <div className="status success">
          ✅ 钱包已连接: {publicKey.toBase58().slice(0, 8)}...{publicKey.toBase58().slice(-8)}
        </div>
        <button className="button" onClick={handleDisconnect}>
          断开连接
        </button>
      </div>
    );
  }

  return (
    <div>
      <div className="status info">
        ℹ️ 请连接 Phantom 钱包以开始使用
      </div>
      <button
        className="button"
        onClick={connectPhantom}
        disabled={isConnecting}
      >
        {isConnecting ? '连接中...' : '连接 Phantom 钱包'}
      </button>

      {!window.solana && (
        <div className="status error" style={{ marginTop: '10px' }}>
          ⚠️ 未检测到 Phantom 钱包，请先安装 Phantom 浏览器扩展
          <br />
          <a
            href="https://phantom.app/"
            target="_blank"
            rel="noopener noreferrer"
            style={{ color: '#007bff', textDecoration: 'underline' }}
          >
            下载 Phantom 钱包
          </a>
        </div>
      )}
    </div>
  );
};

export default ConnectWallet;
