/**
 * This code was GENERATED using the solita package.
 * Please DO NOT EDIT THIS FILE, instead rerun solita to update it or write a wrapper to add functionality.
 *
 * See: https://github.com/metaplex-foundation/solita
 */

import * as web3 from '@solana/web3.js'
import * as beet from '@metaplex-foundation/beet'
import * as beetSolana from '@metaplex-foundation/beet-solana'
import { Member, memberBeet } from './Member'
export type MultisigCreateArgsV2 = {
  configAuthority: beet.COption<web3.PublicKey>
  threshold: number
  members: Member[]
  timeLock: number
  rentCollector: beet.COption<web3.PublicKey>
  memo: beet.COption<string>
}

/**
 * @category userTypes
 * @category generated
 */
export const multisigCreateArgsV2Beet =
  new beet.FixableBeetArgsStruct<MultisigCreateArgsV2>(
    [
      ['configAuthority', beet.coption(beetSolana.publicKey)],
      ['threshold', beet.u16],
      ['members', beet.array(memberBeet)],
      ['timeLock', beet.u32],
      ['rentCollector', beet.coption(beetSolana.publicKey)],
      ['memo', beet.coption(beet.utf8String)],
    ],
    'MultisigCreateArgsV2'
  )
