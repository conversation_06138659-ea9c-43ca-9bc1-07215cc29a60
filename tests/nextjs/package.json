{"name": "nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@solana/web3.js": "^1.91.0", "@sqds/multisig": "file:../../sdk/multisig", "@types/node": "20.11.25", "@types/react": "18.2.64", "@types/react-dom": "18.2.21", "autoprefixer": "10.4.18", "eslint": "8.57.0", "eslint-config-next": "14.1.3", "next": "14.1.3", "postcss": "8.4.35", "react": "18.2.0", "react-dom": "18.2.0", "tailwindcss": "3.4.1", "typescript": "5.4.2"}}