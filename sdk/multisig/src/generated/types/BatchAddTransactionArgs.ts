/**
 * This code was GENERATED using the solita package.
 * Please DO NOT EDIT THIS FILE, instead rerun solita to update it or write a wrapper to add functionality.
 *
 * See: https://github.com/metaplex-foundation/solita
 */

import * as beet from '@metaplex-foundation/beet'
export type BatchAddTransactionArgs = {
  ephemeralSigners: number
  transactionMessage: Uint8Array
}

/**
 * @category userTypes
 * @category generated
 */
export const batchAddTransactionArgsBeet =
  new beet.FixableBeetArgsStruct<BatchAddTransactionArgs>(
    [
      ['ephemeralSigners', beet.u8],
      ['transactionMessage', beet.bytes],
    ],
    'BatchAddTransactionArgs'
  )
