import React from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { useWalletModal } from '@solana/wallet-adapter-react-ui';

const ConnectWallet = () => {
  const { publicKey, disconnect, connected } = useWallet();
  const { setVisible } = useWalletModal();

  const handleConnect = () => {
    // 显示钱包选择模态框
    setVisible(true);
  };

  const handleDisconnect = async () => {
    try {
      await disconnect();
    } catch (error) {
      console.error('断开连接失败:', error);
    }
  };

  if (connected && publicKey) {
    return (
      <div>
        <div className="status success">
          ✅ 钱包已连接: {publicKey.toBase58().slice(0, 8)}...{publicKey.toBase58().slice(-8)}
        </div>
        <button className="button" onClick={handleDisconnect}>
          断开连接
        </button>
      </div>
    );
  }

  return (
    <div>
      <div className="status info">
        ℹ️ 请连接钱包以开始使用
      </div>
      <button className="button" onClick={handleConnect}>
        连接钱包
      </button>

      {!window.solana && (
        <div className="status error" style={{ marginTop: '10px' }}>
          ⚠️ 未检测到 Phantom 钱包，请先安装 Phantom 浏览器扩展
          <br />
          <a
            href="https://phantom.app/"
            target="_blank"
            rel="noopener noreferrer"
            style={{ color: '#007bff', textDecoration: 'underline' }}
          >
            下载 Phantom 钱包
          </a>
        </div>
      )}
    </div>
  );
};

export default ConnectWallet;
