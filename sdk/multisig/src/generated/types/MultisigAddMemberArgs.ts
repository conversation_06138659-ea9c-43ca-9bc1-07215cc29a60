/**
 * This code was GENERATED using the solita package.
 * Please DO NOT EDIT THIS FILE, instead rerun solita to update it or write a wrapper to add functionality.
 *
 * See: https://github.com/metaplex-foundation/solita
 */

import * as beet from '@metaplex-foundation/beet'
import { Member, memberBeet } from './Member'
export type MultisigAddMemberArgs = {
  newMember: Member
  memo: beet.COption<string>
}

/**
 * @category userTypes
 * @category generated
 */
export const multisigAddMemberArgsBeet =
  new beet.FixableBeetArgsStruct<MultisigAddMemberArgs>(
    [
      ['newMember', memberBeet],
      ['memo', beet.coption(beet.utf8String)],
    ],
    'MultisigAddMemberArgs'
  )
