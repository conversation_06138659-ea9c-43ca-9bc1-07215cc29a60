import React, { useState } from 'react';
import { Toaster } from 'react-hot-toast';
import WalletProvider from './components/WalletProvider';
import ConnectWallet from './components/ConnectWallet';
import TransferSol from './components/TransferSol';
import TransferToken from './components/TransferToken';
import TransactionList from './components/TransactionList';
import MemberManagement from './components/MemberManagement';
import MultisigConfig from './components/MultisigConfig';
import ErrorBoundary from './components/ErrorBoundary';

function App() {
  const [activeTab, setActiveTab] = useState('config'); // 默认显示配置页面

  const tabs = [
    { id: 'transfer', label: 'SOL 转账' },
    { id: 'token', label: 'Token 转账' },
    { id: 'transactions', label: '交易列表' },
    { id: 'members', label: '成员管理' },
    { id: 'config', label: '多签配置' },
  ];

  return (
    <ErrorBoundary>
      <WalletProvider>
        <div className="container">
          <Toaster position="top-right" />

          <div className="card">
            <h1>🏦 Squads Multisig Demo</h1>
            <p>基于 Squads Protocol 的多签钱包演示应用</p>
            <ConnectWallet />
          </div>

        <div className="card">
          <div style={{ borderBottom: '1px solid #ddd', marginBottom: '20px' }}>
            {tabs.map(tab => (
              <button
                key={tab.id}
                className={`button ${activeTab === tab.id ? 'active' : ''}`}
                onClick={() => setActiveTab(tab.id)}
                style={{
                  backgroundColor: activeTab === tab.id ? '#007bff' : '#f8f9fa',
                  color: activeTab === tab.id ? 'white' : '#333',
                  border: '1px solid #ddd',
                  borderBottom: activeTab === tab.id ? '2px solid #007bff' : '1px solid #ddd',
                }}
              >
                {tab.label}
              </button>
            ))}
          </div>

          {activeTab === 'transfer' && <TransferSol />}
          {activeTab === 'token' && <TransferToken />}
          {activeTab === 'transactions' && <TransactionList />}
          {activeTab === 'members' && <MemberManagement />}
          {activeTab === 'config' && <MultisigConfig />}
        </div>

        <div className="card">
          <h3>📖 使用说明</h3>
          <ul>
            <li>首先连接 Phantom 钱包</li>
            <li>在"多签配置"中设置多签地址和程序ID</li>
            <li>使用"SOL转账"或"Token转账"创建转账交易</li>
            <li>在"交易列表"中查看和投票批准交易</li>
            <li>达到阈值后可以执行交易</li>
            <li>在"成员管理"中添加或移除多签成员</li>
          </ul>
        </div>
        </div>
      </WalletProvider>
    </ErrorBoundary>
  );
}

export default App;
