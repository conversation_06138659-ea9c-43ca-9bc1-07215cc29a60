## [1.0.16](https://github.com/Squads-Protocol/public-v4-client/compare/v1.0.15...v1.0.16) (2025-03-06)


### Bug Fixes

* **lockfile:** updated lockfile ([c8e1b4a](https://github.com/Squads-Protocol/public-v4-client/commit/c8e1b4a3d209b7c1c3cfc4d9e7edb8e187e22833))

## [1.2.3](https://github.com/Squads-Protocol/public-v4-client/compare/v1.2.2...v1.2.3) (2025-03-13)


### Bug Fixes

* **babel:** dependabot alert ([e5c7392](https://github.com/Squads-Protocol/public-v4-client/commit/e5c7392313bd2baacc1990284238fa2cfa8846ec))
* **threshold-input:** adjusted form display ([9bd7bf1](https://github.com/Squads-Protocol/public-v4-client/commit/9bd7bf1bed3ee95759973a4868544b2d64d65d1d))
* **threshold:** updated threshold form and component props ([4ba207a](https://github.com/Squads-Protocol/public-v4-client/commit/4ba207a325964effad05efa1facaa22dc34658e9))

## [1.2.2](https://github.com/Squads-Protocol/public-v4-client/compare/v1.2.1...v1.2.2) (2025-03-12)


### Bug Fixes

* **confirmation:** better confirmation logic ([6a46192](https://github.com/Squads-Protocol/public-v4-client/commit/6a4619246bb3d6c78e8a3798416387748d438659))
* **confirmations:** better handling in tx import ([884229b](https://github.com/Squads-Protocol/public-v4-client/commit/884229bf4018bd1a51e935676bc7e27eedeb9d6d))
* **confirmations:** fixed false positives ([0aa98bc](https://github.com/Squads-Protocol/public-v4-client/commit/0aa98bc49e4d4d144dc955c7d0c501d21caafb8a))
* **confirmations:** reject on missing confirmations ([c41bda5](https://github.com/Squads-Protocol/public-v4-client/commit/c41bda54a266561a05821b1826ad0ed02a157336))
* **modal:** dismiss modal after success ([5c90919](https://github.com/Squads-Protocol/public-v4-client/commit/5c909195463419e01b581d3cd62016d28def31db))

## [1.2.1](https://github.com/Squads-Protocol/public-v4-client/compare/v1.2.0...v1.2.1) (2025-03-11)


### Bug Fixes

* **nav-error:** updated rpc error flow and nav ([c31a697](https://github.com/Squads-Protocol/public-v4-client/commit/c31a6970a60a8db6410246dde7c61c85542b8378))

## [1.2.0](https://github.com/Squads-Protocol/public-v4-client/compare/v1.1.0...v1.2.0) (2025-03-11)


### Features

* **tx-import:** added vault selector ([881420e](https://github.com/Squads-Protocol/public-v4-client/commit/881420e2e2f1e2a0c411ddfdbe7aa369bcde6b71))

## [1.1.0](https://github.com/Squads-Protocol/public-v4-client/compare/v1.0.16...v1.1.0) (2025-03-10)


### Features

* **confimation:** insure that signature is processed ([83c5f9f](https://github.com/Squads-Protocol/public-v4-client/commit/83c5f9f2c7ba71fe64904043d2dff5038795f227))
* **ms-config-lookup:** set to 300 for sig limit ([7295b1f](https://github.com/Squads-Protocol/public-v4-client/commit/7295b1fcc3cb77c3046f96d92ad72ddbe83cf974))
* **ms-config-lookup:** set to 300 for sig limit ([f0d2580](https://github.com/Squads-Protocol/public-v4-client/commit/f0d2580a24f33d849a0087b3bdafb331ce6da158))
* **scan-for-ms:** scan through vault sigs to find ms ([b2b64ee](https://github.com/Squads-Protocol/public-v4-client/commit/b2b64eec3ae50a6b49d4b79be06380feccd76920))
* **scan-for-ms:** scan through vault sigs to find ms ([7375783](https://github.com/Squads-Protocol/public-v4-client/commit/7375783a0e0fc46f95a59c4366b7b14e59e37f2f))
* **web:** Add Program Manager Page ([9df4dab](https://github.com/Squads-Protocol/public-v4-client/commit/9df4dabb5128a8cf46e480f8e77e3bd26c7f69b7))


### Bug Fixes

* **confirm:** added wait for confirmation logic in utils ([38380ff](https://github.com/Squads-Protocol/public-v4-client/commit/38380ff8472faa72bc6853141db3f2148343561d))
* **error-boundary:** add at page level to capture and preserve nav ([7a35c63](https://github.com/Squads-Protocol/public-v4-client/commit/7a35c63e4e9a63a29420f90455218a95d1f086f0))
* **error-boundary:** include message regarding rpc ([25084e7](https://github.com/Squads-Protocol/public-v4-client/commit/25084e7aa4e63573fde0bc2df3b3ef403b862501))
* **import-tx:** check wallet status ([2a440cd](https://github.com/Squads-Protocol/public-v4-client/commit/2a440cd84c32cc4129394f75e9865124057ea6da))
* **inputs:** added trim to the input fields ([2ef9986](https://github.com/Squads-Protocol/public-v4-client/commit/2ef998612615fde26bc739be8b46a9926cabe1d1))
* **modals:** better modal handling ([8a344a7](https://github.com/Squads-Protocol/public-v4-client/commit/8a344a704e6dac0f44826bc42b7d82aedbcc6c0e))
* **ms-search:** error handling and toast ([0132422](https://github.com/Squads-Protocol/public-v4-client/commit/01324227c0d2438488e5467fc2a85a7e65c2c576))
* **ms-search:** error handling and toast ([300655d](https://github.com/Squads-Protocol/public-v4-client/commit/300655dda6a90e38f2120888102d2c0889fbabfb))

## [1.0.15](https://github.com/Squads-Protocol/public-v4-client/compare/v1.0.14...v1.0.15) (2025-03-06)


### Bug Fixes

* **favicon:** added squads favicon.ico ([cec7b27](https://github.com/Squads-Protocol/public-v4-client/commit/cec7b273545d9fb61f24331b271610be53794632))
* **table:** removed <div> to resolve react warning ([60d4923](https://github.com/Squads-Protocol/public-v4-client/commit/60d4923ce4d6ee372873f3c6e6f9257640bca37a))

## [1.0.14](https://github.com/Squads-Protocol/public-v4-client/compare/v1.0.13...v1.0.14) (2025-03-05)


### Bug Fixes

* **wallet-connect:** error handling for wallet not connected ([8e931b3](https://github.com/Squads-Protocol/public-v4-client/commit/8e931b33d8e39628d8e633037aadb0140286279e))

## [1.0.13](https://github.com/Squads-Protocol/public-v4-client/compare/v1.0.12...v1.0.13) (2025-03-05)


### Bug Fixes

* **transactions:** if page less than 1, set to 1 ([335551f](https://github.com/Squads-Protocol/public-v4-client/commit/335551f868e7b5770e0a24763442feed033ea2d9))

## [1.0.12](https://github.com/Squads-Protocol/public-v4-client/compare/v1.0.11...v1.0.12) (2025-03-05)


### Bug Fixes

* **transactions:** stale label in status ([52b5de3](https://github.com/Squads-Protocol/public-v4-client/commit/52b5de39c06b507e20115df23501703fc8334777))

## [1.0.11](https://github.com/Squads-Protocol/public-v4-client/compare/v1.0.10...v1.0.11) (2025-03-05)


### Bug Fixes

* **transactions:** show (stale) for deprecated txs ([a4ba9eb](https://github.com/Squads-Protocol/public-v4-client/commit/a4ba9ebfb902a3fc00b0ac7802351065f559cd67))

## [1.0.10](https://github.com/Squads-Protocol/public-v4-client/compare/v1.0.9...v1.0.10) (2025-03-05)


### Bug Fixes

* **transactions:** set page list to 10 ([f3ccd6d](https://github.com/Squads-Protocol/public-v4-client/commit/f3ccd6d9313468c6334312ad289537338aa210c6))

## [1.0.9](https://github.com/Squads-Protocol/public-v4-client/compare/v1.0.8...v1.0.9) (2025-03-05)


### Bug Fixes

* **vault-select:** added all 256 vaults ([09e8ebf](https://github.com/Squads-Protocol/public-v4-client/commit/09e8ebfdae739549edf9a85a323ab47669f90cd8))

## [1.0.8](https://github.com/Squads-Protocol/public-v4-client/compare/v1.0.7...v1.0.8) (2025-03-05)


### Bug Fixes

* **switch-squad:** added text to switch squads ([95b9c18](https://github.com/Squads-Protocol/public-v4-client/commit/95b9c18cbc9b37f6947178cc320b66805ec71359))

## [1.0.7](https://github.com/Squads-Protocol/public-v4-client/compare/v1.0.6...v1.0.7) (2025-03-05)


### Bug Fixes

* **add-member:** added check to see if already exists ([54266eb](https://github.com/Squads-Protocol/public-v4-client/commit/54266ebb3f31fad0f8df562019e9919671abf017))

## [1.0.6](https://github.com/Squads-Protocol/public-v4-client/compare/v1.0.5...v1.0.6) (2025-03-05)


### Bug Fixes

* **threshold:** threshold form cleanup ([3041073](https://github.com/Squads-Protocol/public-v4-client/commit/3041073f2087b75b52beb6dab365e9b8083db1c6))

## [1.0.5](https://github.com/Squads-Protocol/public-v4-client/compare/v1.0.4...v1.0.5) (2025-03-05)


### Bug Fixes

* **permissions:** human readable from bitmask ([b5deca6](https://github.com/Squads-Protocol/public-v4-client/commit/b5deca69dec3313f988207fc07d1e3d3f09525af))

## [1.0.4](https://github.com/Squads-Protocol/public-v4-client/compare/v1.0.3...v1.0.4) (2025-03-05)


### Bug Fixes

* **explorer:** custom explorer link support ([956fd85](https://github.com/Squads-Protocol/public-v4-client/commit/956fd85033679cebaeac95abdac9b7cc01fe6c0d))
* **remove-member:** added access hook on remove member ([3b996a5](https://github.com/Squads-Protocol/public-v4-client/commit/3b996a5396565168b51bc45dd837165a5ffaece2))

## [1.0.3](https://github.com/Squads-Protocol/public-v4-client/compare/v1.0.2...v1.0.3) (2025-03-05)


### Bug Fixes

* **pagination:** fixed tx pagination on search/hash ([1d3ceef](https://github.com/Squads-Protocol/public-v4-client/commit/1d3ceef995fd3bd24bacd20c4e5c29677ad955c8))

## [1.0.2](https://github.com/Squads-Protocol/public-v4-client/compare/v1.0.1...v1.0.2) (2025-03-05)


### Bug Fixes

* **access:** useAccess hook added for member check ([a738957](https://github.com/Squads-Protocol/public-v4-client/commit/a738957f6cca520f4cf20a3847bcc3a0beb1c4df))

## [1.0.1](https://github.com/Squads-Protocol/public-v4-client/compare/v1.0.0...v1.0.1) (2025-03-05)


### Bug Fixes

* **hash-router:** added hashrouter for routes ([bfa3f4a](https://github.com/Squads-Protocol/public-v4-client/commit/bfa3f4a3499c320716a4e37e14c2fcd0c65d3b81))

# 1.0.0 (2025-03-05)


### Bug Fixes

* **cleanup:** unused directives and semver ([ee1258f](https://github.com/Squads-Protocol/public-v4-client/commit/ee1258ffa741a0946475c5f2cc725869e94cead4))


### Features

* **first:** initial setup ([22d6179](https://github.com/Squads-Protocol/public-v4-client/commit/22d61794e69076609667a368b7941a2da9ffa6a0))
* **manifest:** added build manifest.json ([f2bf41f](https://github.com/Squads-Protocol/public-v4-client/commit/f2bf41fd13d7db0c161df5c9ec582e2dd3421c0b))
