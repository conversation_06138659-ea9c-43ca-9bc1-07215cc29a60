/**
 * This code was GENERATED using the solita package.
 * Please DO NOT EDIT THIS FILE, instead rerun solita to update it or write a wrapper to add functionality.
 *
 * See: https://github.com/metaplex-foundation/solita
 */

import * as beet from '@metaplex-foundation/beet'
export type MultisigChangeThresholdArgs = {
  newThreshold: number
  memo: beet.COption<string>
}

/**
 * @category userTypes
 * @category generated
 */
export const multisigChangeThresholdArgsBeet =
  new beet.FixableBeetArgsStruct<MultisigChangeThresholdArgs>(
    [
      ['newThreshold', beet.u16],
      ['memo', beet.coption(beet.utf8String)],
    ],
    'MultisigChangeThresholdArgs'
  )
