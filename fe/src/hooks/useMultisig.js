import { useState, useEffect } from 'react';
import { PublicKey } from '@solana/web3.js';
import * as multisig from '@sqds/multisig';

// 默认配置 - 留空，需要用户手动配置
const DEFAULT_CONFIG = {
  multisigAddress: '', // 需要用户配置真实的多签地址
  programId: 'SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf', // Squads 程序 ID
  vaultIndex: 0,
};

export const useMultisig = () => {
  const [config, setConfig] = useState(() => {
    // 从 localStorage 读取配置
    const saved = localStorage.getItem('multisig-config');
    return saved ? JSON.parse(saved) : DEFAULT_CONFIG;
  });

  // 保存配置到 localStorage
  useEffect(() => {
    localStorage.setItem('multisig-config', JSON.stringify(config));
  }, [config]);

  const updateConfig = (newConfig) => {
    setConfig(prev => ({ ...prev, ...newConfig }));
  };

  // 获取多签 PDA
  const getMultisigPda = () => {
    if (!config.multisigAddress) return null;
    try {
      return new PublicKey(config.multisigAddress);
    } catch {
      return null;
    }
  };

  // 获取程序 ID
  const getProgramId = () => {
    if (!config.programId) return multisig.PROGRAM_ID;
    try {
      return new PublicKey(config.programId);
    } catch {
      return multisig.PROGRAM_ID;
    }
  };

  // 获取金库地址
  const getVaultPda = () => {
    const multisigPda = getMultisigPda();
    const programId = getProgramId();

    if (!multisigPda) return null;

    try {
      return multisig.getVaultPda({
        multisigPda,
        index: config.vaultIndex,
        programId,
      })[0];
    } catch {
      return null;
    }
  };

  return {
    config,
    updateConfig,
    getMultisigPda,
    getProgramId,
    getVaultPda,
  };
};
