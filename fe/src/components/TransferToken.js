import React, { useState } from 'react';
import { useWallet, useConnection } from '@solana/wallet-adapter-react';
import { PublicKey, TransactionMessage, VersionedTransaction } from '@solana/web3.js';
import {
  getAssociatedTokenAddress,
  createTransferCheckedInstruction,
  createAssociatedTokenAccountIdempotentInstruction,
  TOKEN_PROGRAM_ID
} from '@solana/spl-token';
import * as multisig from '@sqds/multisig';
import { useMultisig } from '../hooks/useMultisig';
import toast from 'react-hot-toast';

// 常用 Token 配置
const TOKENS = {
  usdc: {
    mint: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
    decimals: 6,
    name: 'USDC',
  },
  usdt: {
    mint: 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB',
    decimals: 6,
    name: 'USDT',
  },
};

const TransferToken = () => {
  const { publicKey, sendTransaction } = useWallet();
  const { connection } = useConnection();
  const { getMultisigPda, getProgramId, getVaultPda } = useMultisig();

  const [formData, setFormData] = useState({
    token: 'usdc',
    recipient: '',
    amount: '',
    memo: '',
    customMint: '',
    customDecimals: 6,
  });
  const [loading, setLoading] = useState(false);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'customDecimals' ? parseInt(value) || 6 : value
    }));
  };

  const getTokenConfig = () => {
    if (formData.token === 'custom') {
      return {
        mint: formData.customMint,
        decimals: formData.customDecimals,
        name: 'Custom Token',
      };
    }
    return TOKENS[formData.token];
  };

  const handleTransfer = async () => {
    if (!publicKey) {
      toast.error('请先连接钱包');
      return;
    }

    const multisigPda = getMultisigPda();
    const programId = getProgramId();
    const vaultPda = getVaultPda();

    if (!multisigPda || !vaultPda) {
      toast.error('请先在"多签配置"中设置多签地址');
      return;
    }

    if (!formData.recipient || !formData.amount) {
      toast.error('请填写收款地址和转账金额');
      return;
    }

    const tokenConfig = getTokenConfig();
    if (!tokenConfig.mint) {
      toast.error('请选择 Token 或输入自定义 Token 地址');
      return;
    }

    setLoading(true);

    try {
      // 验证地址
      const recipientPubkey = new PublicKey(formData.recipient);
      const mintPubkey = new PublicKey(tokenConfig.mint);
      const transferAmount = BigInt(Math.floor(parseFloat(formData.amount) * Math.pow(10, tokenConfig.decimals)));

      if (transferAmount <= 0n) {
        throw new Error('转账金额必须大于 0');
      }

      // 获取 Token 程序信息
      const mintAccountInfo = await connection.getAccountInfo(mintPubkey);
      const TOKEN_PROGRAM = mintAccountInfo?.owner || TOKEN_PROGRAM_ID;

      // 获取关联 Token 账户地址
      const vaultTokenAccount = await getAssociatedTokenAddress(
        mintPubkey,
        vaultPda,
        true,
        TOKEN_PROGRAM
      );

      const recipientTokenAccount = await getAssociatedTokenAddress(
        mintPubkey,
        recipientPubkey,
        false,
        TOKEN_PROGRAM
      );

      // 检查金库 Token 余额
      try {
        const vaultAccount = await connection.getTokenAccountBalance(vaultTokenAccount);
        const vaultBalance = BigInt(vaultAccount.value.amount);

        if (vaultBalance < transferAmount) {
          throw new Error(`${tokenConfig.name} 余额不足。当前余额: ${Number(vaultBalance) / Math.pow(10, tokenConfig.decimals)}`);
        }
      } catch (error) {
        if (error.message.includes('could not find account')) {
          throw new Error(`金库没有 ${tokenConfig.name} Token 账户`);
        }
        throw error;
      }

      // 获取多签信息
      const multisigInfo = await multisig.accounts.Multisig.fromAccountAddress(
        connection,
        multisigPda,
        undefined,
        programId
      );

      // 创建接收方关联 Token 账户指令（如果不存在）
      const createRecipientATAInstruction = createAssociatedTokenAccountIdempotentInstruction(
        vaultPda,
        recipientTokenAccount,
        recipientPubkey,
        mintPubkey,
        TOKEN_PROGRAM
      );

      // 创建转账指令
      const transferInstruction = createTransferCheckedInstruction(
        vaultTokenAccount,
        mintPubkey,
        recipientTokenAccount,
        vaultPda,
        transferAmount,
        tokenConfig.decimals,
        [],
        TOKEN_PROGRAM
      );

      // 创建交易消息
      const { blockhash } = await connection.getLatestBlockhash();
      const transferMessage = new TransactionMessage({
        payerKey: vaultPda,
        recentBlockhash: blockhash,
        instructions: [createRecipientATAInstruction, transferInstruction],
      });

      const transactionIndex = BigInt(multisigInfo.transactionIndex) + 1n;

      // 创建多签交易指令
      const createIx = multisig.instructions.vaultTransactionCreate({
        multisigPda,
        transactionIndex,
        creator: publicKey,
        vaultIndex: 0,
        ephemeralSigners: 0,
        transactionMessage: transferMessage,
        memo: formData.memo || `Transfer ${formData.amount} ${tokenConfig.name}`,
        programId,
      });

      // 创建提案指令
      const proposalIx = multisig.instructions.proposalCreate({
        multisigPda,
        transactionIndex,
        creator: publicKey,
        isDraft: false,
        programId,
      });

      // 创建投票指令
      const approveIx = multisig.instructions.proposalApprove({
        multisigPda,
        transactionIndex,
        member: publicKey,
        programId,
      });

      // 组合所有指令
      const combinedMessage = new TransactionMessage({
        payerKey: publicKey,
        recentBlockhash: blockhash,
        instructions: [createIx, proposalIx, approveIx],
      });

      const transaction = new VersionedTransaction(combinedMessage.compileToV0Message());

      // 发送交易
      const signature = await sendTransaction(transaction, connection, {
        skipPreflight: true,
      });

      toast.loading('确认交易中...', { id: 'transfer' });
      await connection.confirmTransaction(signature);

      toast.success(`${tokenConfig.name} 转账交易已创建并投票！交易索引: ${transactionIndex}`, { id: 'transfer' });

      // 清空表单
      setFormData(prev => ({ ...prev, recipient: '', amount: '', memo: '' }));

    } catch (error) {
      console.error('Token 转账失败:', error);
      toast.error('Token 转账失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <h3>🪙 Token 转账</h3>

      <div className="form-group">
        <label className="label">选择 Token</label>
        <select
          name="token"
          value={formData.token}
          onChange={handleInputChange}
          className="input"
          disabled={loading}
        >
          <option value="usdc">USDC</option>
          <option value="usdt">USDT</option>
          <option value="custom">自定义 Token</option>
        </select>
      </div>

      {formData.token === 'custom' && (
        <>
          <div className="form-group">
            <label className="label">Token Mint 地址</label>
            <input
              type="text"
              name="customMint"
              value={formData.customMint}
              onChange={handleInputChange}
              className="input"
              placeholder="输入 Token Mint 地址..."
              disabled={loading}
            />
          </div>
          <div className="form-group">
            <label className="label">Token 精度</label>
            <input
              type="number"
              name="customDecimals"
              value={formData.customDecimals}
              onChange={handleInputChange}
              className="input"
              min="0"
              max="18"
              disabled={loading}
            />
          </div>
        </>
      )}

      <div className="form-group">
        <label className="label">收款地址</label>
        <input
          type="text"
          name="recipient"
          value={formData.recipient}
          onChange={handleInputChange}
          className="input"
          placeholder="输入收款地址..."
          disabled={loading}
        />
      </div>

      <div className="form-group">
        <label className="label">转账金额</label>
        <input
          type="number"
          name="amount"
          value={formData.amount}
          onChange={handleInputChange}
          className="input"
          placeholder="0.01"
          step="0.000001"
          min="0"
          disabled={loading}
        />
      </div>

      <div className="form-group">
        <label className="label">备注 (可选)</label>
        <input
          type="text"
          name="memo"
          value={formData.memo}
          onChange={handleInputChange}
          className="input"
          placeholder="转账备注..."
          disabled={loading}
        />
      </div>

      <button
        className="button"
        onClick={handleTransfer}
        disabled={!publicKey || loading}
      >
        {loading ? '处理中...' : `创建 ${getTokenConfig().name} 转账`}
      </button>

      <div className="status info">
        <h4>📝 操作说明</h4>
        <ul>
          <li>选择要转账的 Token 类型或输入自定义 Token</li>
          <li>输入有效的 Solana 地址作为收款地址</li>
          <li>输入转账金额（根据 Token 精度）</li>
          <li>系统会自动创建接收方的关联 Token 账户</li>
          <li>点击创建转账将创建交易并自动投票</li>
        </ul>
      </div>
    </div>
  );
};

export default TransferToken;
