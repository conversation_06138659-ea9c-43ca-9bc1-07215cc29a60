import React from 'react';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="container">
          <div className="card">
            <h2>⚠️ 应用出现错误</h2>
            <div className="status error">
              <p><strong>错误信息:</strong> {this.state.error?.message || '未知错误'}</p>
              <p>请刷新页面重试，或检查浏览器控制台获取更多信息。</p>
            </div>
            <button 
              className="button" 
              onClick={() => window.location.reload()}
            >
              刷新页面
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
