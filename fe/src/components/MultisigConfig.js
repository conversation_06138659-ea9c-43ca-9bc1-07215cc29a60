import React, { useState } from 'react';
import { useMultisig } from '../hooks/useMultisig';
import toast from 'react-hot-toast';

const MultisigConfig = () => {
  const { config, updateConfig } = useMultisig();
  const [formData, setFormData] = useState({
    multisigAddress: config.multisigAddress || '',
    programId: config.programId || '',
    vaultIndex: config.vaultIndex || 0,
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'vaultIndex' ? parseInt(value) || 0 : value,
    }));
  };

  const handleSave = () => {
    try {
      updateConfig(formData);
      toast.success('配置已保存');
    } catch (error) {
      toast.error('保存配置失败: ' + error.message);
    }
  };

  const handleReset = () => {
    const defaultConfig = {
      multisigAddress: '',
      programId: 'SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf',
      vaultIndex: 0,
    };
    setFormData(defaultConfig);
    updateConfig(defaultConfig);
    toast.success('已重置为默认配置');
  };

  const createTestMultisig = () => {
    toast.info('创建测试多签功能开发中...');
    // TODO: 实现创建测试多签的功能
  };

  return (
    <div>
      <h3>⚙️ 多签配置</h3>

      <div className="form-group">
        <label className="label">多签地址 (Multisig PDA)</label>
        <input
          type="text"
          name="multisigAddress"
          value={formData.multisigAddress}
          onChange={handleInputChange}
          className="input"
          placeholder="输入多签地址..."
        />
      </div>

      <div className="form-group">
        <label className="label">程序 ID (Program ID)</label>
        <input
          type="text"
          name="programId"
          value={formData.programId}
          onChange={handleInputChange}
          className="input"
          placeholder="输入程序 ID..."
        />
      </div>

      <div className="form-group">
        <label className="label">金库索引 (Vault Index)</label>
        <input
          type="number"
          name="vaultIndex"
          value={formData.vaultIndex}
          onChange={handleInputChange}
          className="input"
          min="0"
          placeholder="0"
        />
      </div>

      <div>
        <button className="button" onClick={handleSave}>
          保存配置
        </button>
        <button
          className="button"
          onClick={handleReset}
          style={{ backgroundColor: '#6c757d' }}
        >
          重置默认
        </button>
        <button
          className="button"
          onClick={createTestMultisig}
          style={{ backgroundColor: '#28a745' }}
        >
          创建测试多签
        </button>
      </div>

      <div className="status info" style={{ marginTop: '20px' }}>
        <h4>📋 当前配置</h4>
        <p><strong>多签地址:</strong> {config.multisigAddress || '未设置'}</p>
        <p><strong>程序 ID:</strong> {config.programId || '未设置'}</p>
        <p><strong>金库索引:</strong> {config.vaultIndex}</p>
      </div>

      <div className="status info">
        <h4>💡 配置说明</h4>
        <ul>
          <li><strong>多签地址:</strong> 您的多签钱包地址 (PDA)</li>
          <li><strong>程序 ID:</strong> Squads 多签程序的地址</li>
          <li><strong>金库索引:</strong> 多签钱包的金库索引，通常为 0</li>
        </ul>
      </div>

      <div className="status error">
        <h4>⚠️ 重要提示</h4>
        <ul>
          <li>当前为演示环境，需要配置真实的多签地址才能使用</li>
          <li>如果您还没有多签钱包，请先创建一个</li>
          <li>可以使用 Squads 官方应用创建多签钱包: <a href="https://v4.squads.so/" target="_blank" rel="noopener noreferrer" style={{ color: '#007bff' }}>https://v4.squads.so/</a></li>
          <li>或者点击"创建测试多签"按钮（功能开发中）</li>
        </ul>
      </div>

      <div className="status info">
        <h4>📝 示例配置</h4>
        <p><strong>多签地址示例:</strong></p>
        <code style={{ background: '#f8f9fa', padding: '5px', borderRadius: '3px', fontSize: '12px' }}>
          2VqKhjZ8aTLBRuXWYBPWVqHqXvzKjbALrHb7JqKqKqKq
        </code>
        <p style={{ fontSize: '12px', color: '#666', marginTop: '5px' }}>
          ⚠️ 这只是格式示例，请使用您自己的真实多签地址
        </p>
      </div>
    </div>
  );
};

export default MultisigConfig;
