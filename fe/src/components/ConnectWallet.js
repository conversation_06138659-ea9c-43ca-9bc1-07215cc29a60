import React, { useState } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { useWalletModal } from '@solana/wallet-adapter-react-ui';

const ConnectWallet = () => {
  const { publicKey, disconnect, connected, wallets, select, connect } = useWallet();
  const { setVisible } = useWalletModal();
  const [connecting, setConnecting] = useState(false);

  const handleConnect = async () => {
    try {
      setConnecting(true);
      console.log('Available wallets:', wallets.map(w => w.adapter.name));

      // 尝试直接连接 Phantom
      const phantomWallet = wallets.find(w =>
        w.adapter.name === 'Phantom' ||
        w.adapter.name.toLowerCase().includes('phantom')
      );

      if (phantomWallet && phantomWallet.adapter.readyState === 'Installed') {
        console.log('Found Phantom wallet, connecting directly...');
        select(phantomWallet.adapter.name);
        await connect();
      } else {
        console.log('Phantom not found or not ready, showing modal...');
        // 显示钱包选择模态框
        setVisible(true);
      }
    } catch (error) {
      console.error('连接失败:', error);
      // 如果直接连接失败，显示模态框
      setVisible(true);
    } finally {
      setConnecting(false);
    }
  };

  const handleDisconnect = async () => {
    try {
      await disconnect();
    } catch (error) {
      console.error('断开连接失败:', error);
    }
  };

  if (connected && publicKey) {
    return (
      <div>
        <div className="status success">
          ✅ 钱包已连接: {publicKey.toBase58().slice(0, 8)}...{publicKey.toBase58().slice(-8)}
        </div>
        <button className="button" onClick={handleDisconnect}>
          断开连接
        </button>
      </div>
    );
  }

  return (
    <div>
      <div className="status info">
        ℹ️ 请连接钱包以开始使用
      </div>
      <button
        className="button"
        onClick={handleConnect}
        disabled={connecting}
      >
        {connecting ? '连接中...' : '连接钱包'}
      </button>

      <div style={{ marginTop: '10px', fontSize: '12px', color: '#666' }}>
        <p>调试信息:</p>
        <p>可用钱包: {wallets.length} 个</p>
        <p>Phantom 状态: {window.solana ? '已安装' : '未安装'}</p>
        {wallets.length > 0 && (
          <p>钱包列表: {wallets.map(w => `${w.adapter.name}(${w.adapter.readyState})`).join(', ')}</p>
        )}
      </div>

      {window.solana && (
        <button
          className="button"
          onClick={async () => {
            try {
              console.log('Testing direct Phantom connection...');
              const response = await window.solana.connect();
              console.log('Direct connection success:', response);
            } catch (error) {
              console.error('Direct connection failed:', error);
            }
          }}
          style={{ backgroundColor: '#9945FF', marginTop: '10px' }}
        >
          测试直接连接 Phantom
        </button>
      )}

      {!window.solana && (
        <div className="status error" style={{ marginTop: '10px' }}>
          ⚠️ 未检测到 Phantom 钱包，请先安装 Phantom 浏览器扩展
          <br />
          <a
            href="https://phantom.app/"
            target="_blank"
            rel="noopener noreferrer"
            style={{ color: '#007bff', textDecoration: 'underline' }}
          >
            下载 Phantom 钱包
          </a>
        </div>
      )}
    </div>
  );
};

export default ConnectWallet;
