{"private": true, "workspaces": ["sdk/multisig", "sdk/rs"], "scripts": {"build": "turbo run build", "test:detached": "turbo run build && anchor test --detach -- --features=testing && echo \"\n⚠️ Don't forget to recompile the .so file before deployment\n\"", "test": "turbo run build && anchor test -- --features=testing && echo \"\n⚠️ Don't forget to recompile the .so file before deployment\n\"", "pretest": "mkdir -p target/deploy && cp ./test-program-keypair.json ./target/deploy/squads_multisig_program-keypair.json", "ts": "turbo run ts && yarn tsc --noEmit"}, "devDependencies": {"@solana/spl-memo": "^0.2.3", "@solana/spl-token": "*", "@types/bn.js": "5.1.0", "@types/mocha": "10.0.1", "@types/node-fetch": "2.6.2", "mocha": "10.2.0", "prettier": "2.6.2", "ts-node": "10.9.1", "turbo": "1.6.3", "typescript": "*"}, "resolutions": {"@solana/web3.js": "1.70.3", "@solana/spl-token": "0.3.6", "typescript": "4.9.4"}, "packageManager": "yarn@4.9.2"}