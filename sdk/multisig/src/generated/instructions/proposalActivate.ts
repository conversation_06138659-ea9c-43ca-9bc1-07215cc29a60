/**
 * This code was GENERATED using the solita package.
 * Please DO NOT EDIT THIS FILE, instead rerun solita to update it or write a wrapper to add functionality.
 *
 * See: https://github.com/metaplex-foundation/solita
 */

import * as beet from '@metaplex-foundation/beet'
import * as web3 from '@solana/web3.js'

/**
 * @category Instructions
 * @category ProposalActivate
 * @category generated
 */
export const proposalActivateStruct = new beet.BeetArgsStruct<{
  instructionDiscriminator: number[] /* size: 8 */
}>(
  [['instructionDiscriminator', beet.uniformFixedSizeArray(beet.u8, 8)]],
  'ProposalActivateInstructionArgs'
)
/**
 * Accounts required by the _proposalActivate_ instruction
 *
 * @property [] multisig
 * @property [_writable_, **signer**] member
 * @property [_writable_] proposal
 * @category Instructions
 * @category ProposalActivate
 * @category generated
 */
export type ProposalActivateInstructionAccounts = {
  multisig: web3.PublicKey
  member: web3.PublicKey
  proposal: web3.PublicKey
  anchorRemainingAccounts?: web3.AccountMeta[]
}

export const proposalActivateInstructionDiscriminator = [
  11, 34, 92, 248, 154, 27, 51, 106,
]

/**
 * Creates a _ProposalActivate_ instruction.
 *
 * @param accounts that will be accessed while the instruction is processed
 * @category Instructions
 * @category ProposalActivate
 * @category generated
 */
export function createProposalActivateInstruction(
  accounts: ProposalActivateInstructionAccounts,
  programId = new web3.PublicKey('SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf')
) {
  const [data] = proposalActivateStruct.serialize({
    instructionDiscriminator: proposalActivateInstructionDiscriminator,
  })
  const keys: web3.AccountMeta[] = [
    {
      pubkey: accounts.multisig,
      isWritable: false,
      isSigner: false,
    },
    {
      pubkey: accounts.member,
      isWritable: true,
      isSigner: true,
    },
    {
      pubkey: accounts.proposal,
      isWritable: true,
      isSigner: false,
    },
  ]

  if (accounts.anchorRemainingAccounts != null) {
    for (const acc of accounts.anchorRemainingAccounts) {
      keys.push(acc)
    }
  }

  const ix = new web3.TransactionInstruction({
    programId,
    keys,
    data,
  })
  return ix
}
