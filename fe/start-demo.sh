#!/bin/bash

# Squads Multisig Demo 启动脚本

echo "🏦 Squads Multisig Demo 启动脚本"
echo "=================================="

# 检查 Node.js 版本
if ! command -v node &> /dev/null; then
    echo "❌ 错误: 未找到 Node.js，请先安装 Node.js"
    exit 1
fi

NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 16 ]; then
    echo "❌ 错误: Node.js 版本过低，需要 16 或更高版本"
    exit 1
fi

echo "✅ Node.js 版本: $(node -v)"

# 检查是否在正确目录
if [ ! -f "package.json" ]; then
    echo "❌ 错误: 请在 fe 目录下运行此脚本"
    exit 1
fi

# 安装依赖
echo ""
echo "📦 安装依赖..."
if npm install; then
    echo "✅ 依赖安装完成"
else
    echo "❌ 依赖安装失败"
    exit 1
fi

# 启动开发服务器
echo ""
echo "🚀 启动开发服务器..."
echo "应用将在 http://localhost:3000 启动"
echo ""
echo "💡 使用说明:"
echo "1. 首先连接 Phantom 钱包"
echo "2. 在'多签配置'中设置多签地址"
echo "3. 使用各个功能标签页进行操作"
echo ""
echo "按 Ctrl+C 停止服务器"
echo ""

npm start
