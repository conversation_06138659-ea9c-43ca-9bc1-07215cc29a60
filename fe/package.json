{"name": "squads-multisig-demo", "version": "1.0.0", "description": "Squads Multisig 前端最小 Demo", "main": "src/App.js", "scripts": {"start": "webpack serve --mode development --open", "build": "webpack --mode production", "dev": "webpack serve --mode development"}, "dependencies": {"@solana/wallet-adapter-base": "^0.9.23", "@solana/wallet-adapter-phantom": "^0.9.24", "@solana/wallet-adapter-react": "^0.15.35", "@solana/wallet-adapter-react-ui": "^0.9.35", "@solana/web3.js": "^1.70.3", "@solana/spl-token": "^0.3.6", "@sqds/multisig": "^2.1.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1"}, "devDependencies": {"@babel/core": "^7.22.0", "@babel/preset-env": "^7.22.0", "@babel/preset-react": "^7.22.0", "babel-loader": "^9.1.0", "css-loader": "^6.8.0", "html-webpack-plugin": "^5.5.0", "style-loader": "^3.3.0", "webpack": "^5.88.0", "webpack-cli": "^5.1.0", "webpack-dev-server": "^4.15.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}