import React, { useState, useEffect } from 'react';
import { useWallet, useConnection } from '@solana/wallet-adapter-react';
import { PublicKey, TransactionMessage, VersionedTransaction } from '@solana/web3.js';
import * as multisig from '@sqds/multisig';
import { useMultisig } from '../hooks/useMultisig';
import toast from 'react-hot-toast';

const MemberManagement = () => {
  const { publicKey, sendTransaction } = useWallet();
  const { connection } = useConnection();
  const { getMultisigPda, getProgramId } = useMultisig();

  const [multisigInfo, setMultisigInfo] = useState(null);
  const [loading, setLoading] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);
  const [newMemberAddress, setNewMemberAddress] = useState('');

  // 加载多签信息
  const loadMultisigInfo = async () => {
    const multisigPda = getMultisigPda();
    const programId = getProgramId();

    if (!multisigPda) {
      toast.error('请先在"多签配置"中设置多签地址');
      return;
    }

    setLoading(true);
    try {
      const info = await multisig.accounts.Multisig.fromAccountAddress(
        connection,
        multisigPda,
        undefined,
        programId
      );
      setMultisigInfo(info);
    } catch (error) {
      console.error('加载多签信息失败:', error);
      toast.error('加载多签信息失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // 添加成员
  const addMember = async () => {
    if (!publicKey) {
      toast.error('请先连接钱包');
      return;
    }

    if (!newMemberAddress) {
      toast.error('请输入新成员地址');
      return;
    }

    const multisigPda = getMultisigPda();
    const programId = getProgramId();

    if (!multisigPda || !multisigInfo) {
      toast.error('请先加载多签信息');
      return;
    }

    setActionLoading(true);

    try {
      // 验证新成员地址
      const newMemberPubkey = new PublicKey(newMemberAddress);

      // 检查成员是否已存在
      const memberExists = multisigInfo.members.some(member => member.key.equals(newMemberPubkey));
      if (memberExists) {
        throw new Error('该地址已经是多签成员');
      }

      // 创建添加成员的配置交易
      const newMembers = [
        ...multisigInfo.members,
        {
          key: newMemberPubkey,
          permissions: multisig.types.Permissions.all(),
        }
      ];

      // 创建配置变更指令
      const configTransactionMessage = new TransactionMessage({
        payerKey: multisigPda,
        recentBlockhash: (await connection.getLatestBlockhash()).blockhash,
        instructions: [
          multisig.instructions.multisigSetConfigAuthority({
            multisigPda,
            configAuthority: multisigInfo.configAuthority,
            newConfigAuthority: multisigInfo.configAuthority,
            programId,
          }),
        ],
      });

      const transactionIndex = BigInt(multisigInfo.transactionIndex) + 1n;

      // 创建配置交易
      const createIx = multisig.instructions.configTransactionCreate({
        multisigPda,
        transactionIndex,
        creator: publicKey,
        actions: [
          {
            __kind: 'AddMember',
            member: {
              key: newMemberPubkey,
              permissions: multisig.types.Permissions.all(),
            },
          },
        ],
        programId,
      });

      // 创建提案
      const proposalIx = multisig.instructions.proposalCreate({
        multisigPda,
        transactionIndex,
        creator: publicKey,
        isDraft: false,
        programId,
      });

      // 投票批准
      const approveIx = multisig.instructions.proposalApprove({
        multisigPda,
        transactionIndex,
        member: publicKey,
        programId,
      });

      // 组合交易
      const { blockhash } = await connection.getLatestBlockhash();
      const combinedMessage = new TransactionMessage({
        payerKey: publicKey,
        recentBlockhash: blockhash,
        instructions: [createIx, proposalIx, approveIx],
      });

      const transaction = new VersionedTransaction(combinedMessage.compileToV0Message());

      // 发送交易
      const signature = await sendTransaction(transaction, connection, {
        skipPreflight: true,
      });

      toast.loading('确认交易中...', { id: 'add-member' });
      await connection.confirmTransaction(signature);

      toast.success(`成员添加交易已创建！交易索引: ${transactionIndex}`, { id: 'add-member' });

      // 清空输入框并重新加载信息
      setNewMemberAddress('');
      await loadMultisigInfo();

    } catch (error) {
      console.error('添加成员失败:', error);
      toast.error('添加成员失败: ' + error.message);
    } finally {
      setActionLoading(false);
    }
  };

  // 移除成员
  const removeMember = async (memberToRemove) => {
    if (!publicKey) {
      toast.error('请先连接钱包');
      return;
    }

    const multisigPda = getMultisigPda();
    const programId = getProgramId();

    if (!multisigPda || !multisigInfo) {
      toast.error('请先加载多签信息');
      return;
    }

    // 检查是否会导致成员数量少于阈值
    if (multisigInfo.members.length - 1 < multisigInfo.threshold) {
      toast.error('移除该成员会导致成员数量少于投票阈值，请先降低阈值');
      return;
    }

    setActionLoading(true);

    try {
      const transactionIndex = BigInt(multisigInfo.transactionIndex) + 1n;

      // 创建配置交易
      const createIx = multisig.instructions.configTransactionCreate({
        multisigPda,
        transactionIndex,
        creator: publicKey,
        actions: [
          {
            __kind: 'RemoveMember',
            oldMember: memberToRemove,
          },
        ],
        programId,
      });

      // 创建提案
      const proposalIx = multisig.instructions.proposalCreate({
        multisigPda,
        transactionIndex,
        creator: publicKey,
        isDraft: false,
        programId,
      });

      // 投票批准
      const approveIx = multisig.instructions.proposalApprove({
        multisigPda,
        transactionIndex,
        member: publicKey,
        programId,
      });

      // 组合交易
      const { blockhash } = await connection.getLatestBlockhash();
      const combinedMessage = new TransactionMessage({
        payerKey: publicKey,
        recentBlockhash: blockhash,
        instructions: [createIx, proposalIx, approveIx],
      });

      const transaction = new VersionedTransaction(combinedMessage.compileToV0Message());

      // 发送交易
      const signature = await sendTransaction(transaction, connection, {
        skipPreflight: true,
      });

      toast.loading('确认交易中...', { id: 'remove-member' });
      await connection.confirmTransaction(signature);

      toast.success(`成员移除交易已创建！交易索引: ${transactionIndex}`, { id: 'remove-member' });

      // 重新加载信息
      await loadMultisigInfo();

    } catch (error) {
      console.error('移除成员失败:', error);
      toast.error('移除成员失败: ' + error.message);
    } finally {
      setActionLoading(false);
    }
  };

  useEffect(() => {
    if (publicKey) {
      loadMultisigInfo();
    }
  }, [publicKey]);

  if (!publicKey) {
    return (
      <div className="status info">
        ℹ️ 请先连接钱包以管理多签成员
      </div>
    );
  }

  return (
    <div>
      <h3>👥 成员管理</h3>

      <div style={{ marginBottom: '20px' }}>
        <button className="button" onClick={loadMultisigInfo} disabled={loading}>
          {loading ? '加载中...' : '刷新信息'}
        </button>
      </div>

      {multisigInfo && (
        <div>
          <div className="status info">
            <h4>📊 多签信息</h4>
            <p><strong>投票阈值:</strong> {multisigInfo.threshold}</p>
            <p><strong>成员数量:</strong> {multisigInfo.members.length}</p>
            <p><strong>配置权限:</strong> {multisigInfo.configAuthority?.toBase58() || '无'}</p>
          </div>

          <div className="card">
            <h4>👤 当前成员列表</h4>
            {multisigInfo.members.map((member, index) => (
              <div key={index} style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                padding: '10px',
                borderBottom: '1px solid #eee',
                marginBottom: '10px'
              }}>
                <div>
                  <p><strong>地址:</strong> {member.key.toBase58()}</p>
                  <p><strong>权限:</strong> 全部权限</p>
                  {member.key.equals(publicKey) && (
                    <span style={{ color: '#007bff', fontWeight: 'bold' }}>（当前用户）</span>
                  )}
                </div>
                <button
                  className="button"
                  onClick={() => removeMember(member.key)}
                  disabled={actionLoading || member.key.equals(publicKey)}
                  style={{ backgroundColor: '#dc3545' }}
                >
                  {member.key.equals(publicKey) ? '无法移除自己' : '移除成员'}
                </button>
              </div>
            ))}
          </div>

          <div className="card">
            <h4>➕ 添加新成员</h4>
            <div className="form-group">
              <label className="label">新成员地址</label>
              <input
                type="text"
                value={newMemberAddress}
                onChange={(e) => setNewMemberAddress(e.target.value)}
                className="input"
                placeholder="输入新成员的 Solana 地址..."
                disabled={actionLoading}
              />
            </div>
            <button
              className="button"
              onClick={addMember}
              disabled={actionLoading || !newMemberAddress}
            >
              {actionLoading ? '处理中...' : '添加成员'}
            </button>
          </div>
        </div>
      )}

      <div className="status info">
        <h4>📝 操作说明</h4>
        <ul>
          <li>添加或移除成员需要创建配置交易并进行投票</li>
          <li>配置交易同样需要达到投票阈值才能执行</li>
          <li>无法移除自己，需要其他成员操作</li>
          <li>移除成员前请确保剩余成员数量不少于投票阈值</li>
          <li>新成员将获得全部权限（投票、执行、配置）</li>
        </ul>
      </div>
    </div>
  );
};

export default MemberManagement;
