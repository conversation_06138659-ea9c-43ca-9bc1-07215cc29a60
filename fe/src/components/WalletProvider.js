import React, { useMemo, useEffect } from 'react';
import { ConnectionProvider, WalletProvider as SolanaWalletProvider } from '@solana/wallet-adapter-react';
import { WalletModalProvider } from '@solana/wallet-adapter-react-ui';
import { clusterApiUrl } from '@solana/web3.js';

// 导入钱包适配器样式
import '@solana/wallet-adapter-react-ui/styles.css';

const WalletProvider = ({ children }) => {
  // 使用 devnet 进行测试
  const network = 'devnet';
  const endpoint = useMemo(() => clusterApiUrl(network), [network]);

  // 使用空数组，让钱包适配器自动检测标准钱包
  const wallets = useMemo(() => [], []);

  // 清理以太坊钱包冲突
  useEffect(() => {
    // 忽略以太坊钱包相关的错误
    const originalError = console.error;
    console.error = (...args) => {
      const message = args[0]?.toString() || '';
      if (
        message.includes('ethereum') ||
        message.includes('MetaMask') ||
        message.includes('Cannot set property ethereum')
      ) {
        return; // 忽略以太坊钱包相关错误
      }
      originalError.apply(console, args);
    };

    return () => {
      console.error = originalError;
    };
  }, []);

  return (
    <ConnectionProvider endpoint={endpoint}>
      <SolanaWalletProvider
        wallets={wallets}
        autoConnect={false}
        onError={(error) => {
          // 忽略以太坊钱包相关错误
          const message = error?.message || '';
          if (
            message.includes('ethereum') ||
            message.includes('MetaMask') ||
            message.includes('Cannot set property ethereum')
          ) {
            return;
          }
          console.error('Wallet error:', error);
        }}
      >
        <WalletModalProvider>
          {children}
        </WalletModalProvider>
      </SolanaWalletProvider>
    </ConnectionProvider>
  );
};

export default WalletProvider;
