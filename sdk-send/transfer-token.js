// Squads 多签 Token 转账工具
// 使用: node transfer-token.js --token=usdc --amount=0.01

import { Connection, Keypair, PublicKey, TransactionMessage, VersionedTransaction } from '@solana/web3.js';
import { getAssociatedTokenAddress, createTransferCheckedInstruction, getAccount } from '@solana/spl-token';
import * as multisig from '@sqds/multisig';

const connection = new Connection("https://api.mainnet-beta.solana.com", "confirmed");
const multisigPda = new PublicKey("HVx9YZh7ZNiNzBkRVyoGc3PxsznCJyv3PPz57fEKVuFr");
const programId = new PublicKey("SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf");
const recipient = new PublicKey("2hvBjn1R3nSXHJop7dz2xTTWSLkQiHSjPmpyjdQY7p7Y");

const TOKENS = {
  usdc: { mint: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v", decimals: 6 },
  usdt: { mint: "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB", decimals: 6 },
};

const memberA = Keypair.fromSecretKey(Buffer.from('dd63f640b1b2e563c2dc8d183db422b69432d795a19f97f25776e8bed89d0d03c66a167c0bb231cbbe7b6ec1e05a63564d0d0da5bb6ca5abc1b88b919851f549', 'hex'));
const memberB = Keypair.fromSecretKey(Buffer.from('4a35f5b76ff4a7949570e1a26a1ee153b60b008b77e116013f8c4ded0da06a8119570e4fedbee23c3bdb3715063be3f042bd3d6ffa25d49831a77ab251906657', 'hex'));

async function main() {
  try {
    const token = process.argv.find(arg => arg.startsWith('--token='))?.split('=')[1]?.toLowerCase() || 'usdc';
    const amount = parseFloat(process.argv.find(arg => arg.startsWith('--amount='))?.split('=')[1] || '0.01');
    
    if (!TOKENS[token]) {
      throw new Error(`不支持的 Token: ${token}，支持: ${Object.keys(TOKENS).join(', ')}`);
    }
    
    console.log(`🚀 开始 ${token.toUpperCase()} 转账，金额: ${amount} ${token.toUpperCase()}`);

    // 检查成员余额
    const memberABalance = await connection.getBalance(memberA.publicKey);
    const memberBBalance = await connection.getBalance(memberB.publicKey);
    console.log(`💳 成员A余额: ${(memberABalance / 1e9).toFixed(6)} SOL`);
    console.log(`💳 成员B余额: ${(memberBBalance / 1e9).toFixed(6)} SOL`);

    if (memberABalance < 0.005 * 1e9) {
      throw new Error(`成员A余额不足，需要至少 0.005 SOL`);
    }

    // 获取多签信息
    const multisigInfo = await multisig.accounts.Multisig.fromAccountAddress(connection, multisigPda);
    console.log(`📋 多签配置: ${multisigInfo.threshold}/${multisigInfo.members.length} 阈值`);

    // 获取金库地址
    const [vaultPda] = multisig.getVaultPda({ multisigPda, index: 0, programId });

    // 创建 Token 转账指令
    const tokenConfig = TOKENS[token];
    const mint = new PublicKey(tokenConfig.mint);
    const vaultTokenAccount = await getAssociatedTokenAddress(mint, vaultPda, true);
    const recipientTokenAccount = await getAssociatedTokenAddress(mint, recipient, false);

    const vaultAccount = await getAccount(connection, vaultTokenAccount);
    const transferAmount = BigInt(Math.floor(amount * Math.pow(10, tokenConfig.decimals)));
    
    console.log(`💰 金库余额: ${Number(vaultAccount.amount) / Math.pow(10, tokenConfig.decimals)} ${token.toUpperCase()}`);
    
    if (vaultAccount.amount < transferAmount) {
      throw new Error(`${token.toUpperCase()} 余额不足`);
    }

    const transferInstruction = createTransferCheckedInstruction(
      vaultTokenAccount,
      mint,
      recipientTokenAccount,
      vaultPda,
      transferAmount,
      tokenConfig.decimals
    );

    const { blockhash } = await connection.getLatestBlockhash();
    const transferMessage = new TransactionMessage({
      payerKey: vaultPda,
      recentBlockhash: blockhash,
      instructions: [transferInstruction],
    });

    const transactionIndex = BigInt(multisigInfo.transactionIndex) + 1n;

    // 成员A创建交易并投票
    console.log('\n📝 成员A创建交易并投票');
    const createIx = multisig.instructions.vaultTransactionCreate({
      multisigPda,
      transactionIndex,
      creator: memberA.publicKey,
      vaultIndex: 0,
      ephemeralSigners: 0,
      transactionMessage: transferMessage,
      memo: `Transfer ${amount} ${token.toUpperCase()}`,
      programId,
    });

    const proposalIx = multisig.instructions.proposalCreate({
      multisigPda,
      transactionIndex,
      creator: memberA.publicKey,
      isDraft: false,
      programId,
    });

    const approveIx = multisig.instructions.proposalApprove({
      multisigPda,
      transactionIndex,
      member: memberA.publicKey,
      programId,
    });

    const combinedMessage = new TransactionMessage({
      payerKey: memberA.publicKey,
      recentBlockhash: blockhash,
      instructions: [createIx, proposalIx, approveIx],
    });

    const combinedTx = new VersionedTransaction(combinedMessage.compileToV0Message());
    combinedTx.sign([memberA]);

    const signature1 = await connection.sendTransaction(combinedTx);
    console.log(`✅ 成员A投票完成: ${signature1}`);
    await connection.confirmTransaction(signature1);

    // 检查是否需要更多投票
    const [proposalPda] = multisig.getProposalPda({ multisigPda, transactionIndex, programId });
    let proposalInfo = await multisig.accounts.Proposal.fromAccountAddress(connection, proposalPda, undefined, programId);
    console.log(`👍 当前投票: ${proposalInfo.approved.length}/${multisigInfo.threshold}`);

    // 如果需要更多投票，成员B投票
    if (proposalInfo.approved.length < multisigInfo.threshold) {
      if (memberBBalance < 0.002 * 1e9) {
        throw new Error(`成员B余额不足，需要至少 0.002 SOL`);
      }
      
      console.log('📝 成员B投票');
      const approveBIx = multisig.instructions.proposalApprove({
        multisigPda,
        transactionIndex,
        member: memberB.publicKey,
        programId,
      });

      const approveMessage = new TransactionMessage({
        payerKey: memberB.publicKey,
        recentBlockhash: (await connection.getLatestBlockhash()).blockhash,
        instructions: [approveBIx],
      });

      const approveTx = new VersionedTransaction(approveMessage.compileToV0Message());
      approveTx.sign([memberB]);

      const signature2 = await connection.sendTransaction(approveTx);
      console.log(`✅ 成员B投票完成: ${signature2}`);
      await connection.confirmTransaction(signature2);

      proposalInfo = await multisig.accounts.Proposal.fromAccountAddress(connection, proposalPda, undefined, programId);
      console.log(`👍 当前投票: ${proposalInfo.approved.length}/${multisigInfo.threshold}`);
    }

    // 执行转账
    if (proposalInfo.approved.length >= multisigInfo.threshold) {
      console.log('⚡ 执行转账');
      const executeSignature = await multisig.rpc.vaultTransactionExecute({
        connection,
        feePayer: memberA,
        multisigPda,
        transactionIndex,
        member: memberA.publicKey,
        programId,
      });

      console.log(`✅ 转账完成: ${executeSignature}`);
      await connection.confirmTransaction(executeSignature);

      const finalAccount = await getAccount(connection, vaultTokenAccount);
      console.log(`💰 转账后余额: ${Number(finalAccount.amount) / Math.pow(10, tokenConfig.decimals)} ${token.toUpperCase()}`);
      console.log(`🎉 ${token.toUpperCase()} 转账完成`);
    } else {
      console.log('❌ 投票不足，无法执行转账');
    }

  } catch (error) {
    console.error('❌ 转账失败:', error.message);
    console.log('💡 使用方式:');
    console.log('  node transfer-token.js --token=usdc --amount=0.01');
    console.log('  node transfer-token.js --token=usdt --amount=0.01');
  }
}

main();
