name: "Setup Solana"
description: "Setup Solana"
runs:
  using: "composite"
  steps:
    - uses: actions/cache@v3
      name: Cache Solana Tool Suite
      id: cache-solana
      with:
        path: |
          ~/.cache/solana/
          ~/.local/share/solana/
        key: solana-${{ runner.os }}-v0000-${{ env.SOLANA_CLI_VERSION }}
    - uses: nick-fields/retry@v2
      if: steps.cache-solana.outputs.cache-hit != 'true'
      with:
        retry_wait_seconds: 300
        timeout_minutes: 2
        max_attempts: 10
        retry_on: error
        shell: bash
        command: sh -c "$(curl -sSfL https://release.solana.com/v${{ env.SOLANA_CLI_VERSION }}/install)"
    - run: echo "/home/<USER>/.local/share/solana/install/active_release/bin" >> $GITHUB_PATH
      shell: bash
    - run: solana-keygen new --no-bip39-passphrase
      shell: bash
    - run: solana config set --url localhost
      shell: bash
