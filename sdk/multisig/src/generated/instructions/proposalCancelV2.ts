/**
 * This code was GENERATED using the solita package.
 * Please DO NOT EDIT THIS FILE, instead rerun solita to update it or write a wrapper to add functionality.
 *
 * See: https://github.com/metaplex-foundation/solita
 */

import * as beet from '@metaplex-foundation/beet'
import * as web3 from '@solana/web3.js'
import {
  ProposalVoteArgs,
  proposalVoteArgsBeet,
} from '../types/ProposalVoteArgs'

/**
 * @category Instructions
 * @category ProposalCancelV2
 * @category generated
 */
export type ProposalCancelV2InstructionArgs = {
  args: ProposalVoteArgs
}
/**
 * @category Instructions
 * @category ProposalCancelV2
 * @category generated
 */
export const proposalCancelV2Struct = new beet.FixableBeetArgsStruct<
  ProposalCancelV2InstructionArgs & {
    instructionDiscriminator: number[] /* size: 8 */
  }
>(
  [
    ['instructionDiscriminator', beet.uniformFixedSizeArray(beet.u8, 8)],
    ['args', proposalVoteArgsBeet],
  ],
  'ProposalCancelV2InstructionArgs'
)
/**
 * Accounts required by the _proposalCancelV2_ instruction
 *
 * @property [] proposalVoteItemMultisig
 * @property [_writable_, **signer**] proposalVoteItemMember
 * @property [_writable_] proposalVoteItemProposal
 * @category Instructions
 * @category ProposalCancelV2
 * @category generated
 */
export type ProposalCancelV2InstructionAccounts = {
  proposalVoteItemMultisig: web3.PublicKey
  proposalVoteItemMember: web3.PublicKey
  proposalVoteItemProposal: web3.PublicKey
  systemProgram?: web3.PublicKey
  anchorRemainingAccounts?: web3.AccountMeta[]
}

export const proposalCancelV2InstructionDiscriminator = [
  205, 41, 194, 61, 220, 139, 16, 247,
]

/**
 * Creates a _ProposalCancelV2_ instruction.
 *
 * @param accounts that will be accessed while the instruction is processed
 * @param args to provide as instruction data to the program
 *
 * @category Instructions
 * @category ProposalCancelV2
 * @category generated
 */
export function createProposalCancelV2Instruction(
  accounts: ProposalCancelV2InstructionAccounts,
  args: ProposalCancelV2InstructionArgs,
  programId = new web3.PublicKey('SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf')
) {
  const [data] = proposalCancelV2Struct.serialize({
    instructionDiscriminator: proposalCancelV2InstructionDiscriminator,
    ...args,
  })
  const keys: web3.AccountMeta[] = [
    {
      pubkey: accounts.proposalVoteItemMultisig,
      isWritable: false,
      isSigner: false,
    },
    {
      pubkey: accounts.proposalVoteItemMember,
      isWritable: true,
      isSigner: true,
    },
    {
      pubkey: accounts.proposalVoteItemProposal,
      isWritable: true,
      isSigner: false,
    },
    {
      pubkey: accounts.systemProgram ?? web3.SystemProgram.programId,
      isWritable: false,
      isSigner: false,
    },
  ]

  if (accounts.anchorRemainingAccounts != null) {
    for (const acc of accounts.anchorRemainingAccounts) {
      keys.push(acc)
    }
  }

  const ix = new web3.TransactionInstruction({
    programId,
    keys,
    data,
  })
  return ix
}
