/**
 * This code was GENERATED using the solita package.
 * Please DO NOT EDIT THIS FILE, instead rerun solita to update it or write a wrapper to add functionality.
 *
 * See: https://github.com/metaplex-foundation/solita
 */

import * as web3 from '@solana/web3.js'
import * as beet from '@metaplex-foundation/beet'
import * as beetSolana from '@metaplex-foundation/beet-solana'
import {
  VaultTransactionMessage,
  vaultTransactionMessageBeet,
} from '../types/VaultTransactionMessage'

/**
 * Arguments used to create {@link VaultTransaction}
 * @category Accounts
 * @category generated
 */
export type VaultTransactionArgs = {
  multisig: web3.PublicKey
  creator: web3.PublicKey
  index: beet.bignum
  bump: number
  vaultIndex: number
  vaultBump: number
  ephemeralSignerBumps: Uint8Array
  message: VaultTransactionMessage
}

export const vaultTransactionDiscriminator = [
  168, 250, 162, 100, 81, 14, 162, 207,
]
/**
 * Holds the data for the {@link VaultTransaction} Account and provides de/serialization
 * functionality for that data
 *
 * @category Accounts
 * @category generated
 */
export class VaultTransaction implements VaultTransactionArgs {
  private constructor(
    readonly multisig: web3.PublicKey,
    readonly creator: web3.PublicKey,
    readonly index: beet.bignum,
    readonly bump: number,
    readonly vaultIndex: number,
    readonly vaultBump: number,
    readonly ephemeralSignerBumps: Uint8Array,
    readonly message: VaultTransactionMessage
  ) {}

  /**
   * Creates a {@link VaultTransaction} instance from the provided args.
   */
  static fromArgs(args: VaultTransactionArgs) {
    return new VaultTransaction(
      args.multisig,
      args.creator,
      args.index,
      args.bump,
      args.vaultIndex,
      args.vaultBump,
      args.ephemeralSignerBumps,
      args.message
    )
  }

  /**
   * Deserializes the {@link VaultTransaction} from the data of the provided {@link web3.AccountInfo}.
   * @returns a tuple of the account data and the offset up to which the buffer was read to obtain it.
   */
  static fromAccountInfo(
    accountInfo: web3.AccountInfo<Buffer>,
    offset = 0
  ): [VaultTransaction, number] {
    return VaultTransaction.deserialize(accountInfo.data, offset)
  }

  /**
   * Retrieves the account info from the provided address and deserializes
   * the {@link VaultTransaction} from its data.
   *
   * @throws Error if no account info is found at the address or if deserialization fails
   */
  static async fromAccountAddress(
    connection: web3.Connection,
    address: web3.PublicKey,
    commitmentOrConfig?: web3.Commitment | web3.GetAccountInfoConfig
  ): Promise<VaultTransaction> {
    const accountInfo = await connection.getAccountInfo(
      address,
      commitmentOrConfig
    )
    if (accountInfo == null) {
      throw new Error(`Unable to find VaultTransaction account at ${address}`)
    }
    return VaultTransaction.fromAccountInfo(accountInfo, 0)[0]
  }

  /**
   * Provides a {@link web3.Connection.getProgramAccounts} config builder,
   * to fetch accounts matching filters that can be specified via that builder.
   *
   * @param programId - the program that owns the accounts we are filtering
   */
  static gpaBuilder(
    programId: web3.PublicKey = new web3.PublicKey(
      'SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf'
    )
  ) {
    return beetSolana.GpaBuilder.fromStruct(programId, vaultTransactionBeet)
  }

  /**
   * Deserializes the {@link VaultTransaction} from the provided data Buffer.
   * @returns a tuple of the account data and the offset up to which the buffer was read to obtain it.
   */
  static deserialize(buf: Buffer, offset = 0): [VaultTransaction, number] {
    return vaultTransactionBeet.deserialize(buf, offset)
  }

  /**
   * Serializes the {@link VaultTransaction} into a Buffer.
   * @returns a tuple of the created Buffer and the offset up to which the buffer was written to store it.
   */
  serialize(): [Buffer, number] {
    return vaultTransactionBeet.serialize({
      accountDiscriminator: vaultTransactionDiscriminator,
      ...this,
    })
  }

  /**
   * Returns the byteSize of a {@link Buffer} holding the serialized data of
   * {@link VaultTransaction} for the provided args.
   *
   * @param args need to be provided since the byte size for this account
   * depends on them
   */
  static byteSize(args: VaultTransactionArgs) {
    const instance = VaultTransaction.fromArgs(args)
    return vaultTransactionBeet.toFixedFromValue({
      accountDiscriminator: vaultTransactionDiscriminator,
      ...instance,
    }).byteSize
  }

  /**
   * Fetches the minimum balance needed to exempt an account holding
   * {@link VaultTransaction} data from rent
   *
   * @param args need to be provided since the byte size for this account
   * depends on them
   * @param connection used to retrieve the rent exemption information
   */
  static async getMinimumBalanceForRentExemption(
    args: VaultTransactionArgs,
    connection: web3.Connection,
    commitment?: web3.Commitment
  ): Promise<number> {
    return connection.getMinimumBalanceForRentExemption(
      VaultTransaction.byteSize(args),
      commitment
    )
  }

  /**
   * Returns a readable version of {@link VaultTransaction} properties
   * and can be used to convert to JSON and/or logging
   */
  pretty() {
    return {
      multisig: this.multisig.toBase58(),
      creator: this.creator.toBase58(),
      index: (() => {
        const x = <{ toNumber: () => number }>this.index
        if (typeof x.toNumber === 'function') {
          try {
            return x.toNumber()
          } catch (_) {
            return x
          }
        }
        return x
      })(),
      bump: this.bump,
      vaultIndex: this.vaultIndex,
      vaultBump: this.vaultBump,
      ephemeralSignerBumps: this.ephemeralSignerBumps,
      message: this.message,
    }
  }
}

/**
 * @category Accounts
 * @category generated
 */
export const vaultTransactionBeet = new beet.FixableBeetStruct<
  VaultTransaction,
  VaultTransactionArgs & {
    accountDiscriminator: number[] /* size: 8 */
  }
>(
  [
    ['accountDiscriminator', beet.uniformFixedSizeArray(beet.u8, 8)],
    ['multisig', beetSolana.publicKey],
    ['creator', beetSolana.publicKey],
    ['index', beet.u64],
    ['bump', beet.u8],
    ['vaultIndex', beet.u8],
    ['vaultBump', beet.u8],
    ['ephemeralSignerBumps', beet.bytes],
    ['message', vaultTransactionMessageBeet],
  ],
  VaultTransaction.fromArgs,
  'VaultTransaction'
)
