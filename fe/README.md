# Squads Multisig Demo

基于 Squads Protocol 的多签钱包前端演示应用，支持 SOL 和 Token 转账、交易投票、成员管理等功能。

## 🚀 快速开始

### 1. 安装依赖

```bash
cd fe
npm install
```

### 2. 启动开发服务器

```bash
npm start
```

应用将在 `http://localhost:3000` 启动。

### 3. 构建生产版本

```bash
npm run build
```

## 📋 功能特性

### ✅ 已实现功能

- **钱包连接**: 支持 Phantom 钱包连接
- **SOL 转账**: 创建 SOL 转账交易并自动投票
- **Token 转账**: 支持 USDC、USDT 和自定义 Token 转账
- **交易管理**: 查看交易列表、投票批准、执行交易
- **成员管理**: 添加和移除多签成员
- **配置管理**: 设置多签地址、程序 ID 等配置

### 🎯 核心流程

1. **连接钱包** → 连接 Phantom 钱包
2. **配置多签** → 设置多签地址和程序 ID
3. **创建交易** → 创建 SOL 或 Token 转账交易
4. **投票批准** → 多签成员对交易进行投票
5. **执行交易** → 达到阈值后执行交易

## 🛠️ 技术栈

- **前端框架**: React 18
- **构建工具**: Webpack 5
- **钱包适配**: @solana/wallet-adapter-react
- **多签 SDK**: @sqds/multisig
- **区块链**: Solana Web3.js
- **通知组件**: react-hot-toast

## 📁 项目结构

```
fe/
├── src/
│   ├── components/          # React 组件
│   │   ├── WalletProvider.js    # 钱包提供者
│   │   ├── ConnectWallet.js     # 连接钱包组件
│   │   ├── TransferSol.js       # SOL 转账组件
│   │   ├── TransferToken.js     # Token 转账组件
│   │   ├── TransactionList.js   # 交易列表组件
│   │   ├── MemberManagement.js  # 成员管理组件
│   │   └── MultisigConfig.js    # 多签配置组件
│   ├── hooks/               # 自定义 Hooks
│   │   └── useMultisig.js       # 多签相关 Hook
│   ├── App.js              # 主应用组件
│   └── index.js            # 应用入口
├── public/
│   └── index.html          # HTML 模板
├── package.json            # 项目配置
├── webpack.config.js       # Webpack 配置
└── README.md              # 项目说明
```

## 🔧 配置说明

### 默认配置

- **网络**: Devnet (测试网络)
- **多签地址**: `HVx9YZh7ZNiNzBkRVyoGc3PxsznCJyv3PPz57fEKVuFr`
- **程序 ID**: `SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf`
- **金库索引**: 0

### 自定义配置

在"多签配置"标签页中可以修改：
- 多签地址 (Multisig PDA)
- 程序 ID (Program ID)
- 金库索引 (Vault Index)

配置会自动保存到浏览器本地存储。

## 💡 使用指南

### 1. 首次使用

1. 点击"连接钱包"按钮连接 Phantom 钱包
2. 在"多签配置"中设置您的多签地址
3. 确保钱包中有足够的 SOL 用于交易费用

### 2. 创建转账

**SOL 转账:**
1. 切换到"SOL 转账"标签
2. 输入收款地址和转账金额
3. 点击"创建 SOL 转账"

**Token 转账:**
1. 切换到"Token 转账"标签
2. 选择 Token 类型或输入自定义 Token
3. 输入收款地址和转账金额
4. 点击"创建 Token 转账"

### 3. 管理交易

1. 在"交易列表"中查看所有交易
2. 对未投票的交易点击"投票批准"
3. 达到阈值的交易点击"执行交易"

### 4. 成员管理

1. 在"成员管理"中查看当前成员
2. 输入新成员地址并点击"添加成员"
3. 对不需要的成员点击"移除成员"

## ⚠️ 注意事项

1. **测试环境**: 当前配置为 Devnet，请勿用于主网资产
2. **交易费用**: 确保钱包有足够 SOL 支付交易费用
3. **多签阈值**: 移除成员前确保剩余成员数不少于阈值
4. **网络延迟**: 交易确认可能需要几秒钟时间
5. **错误处理**: 如遇错误请检查控制台日志

## 🔗 相关链接

- [Squads Protocol 官网](https://squads.so/)
- [Squads SDK 文档](https://docs.squads.so/)
- [Solana 官方文档](https://docs.solana.com/)
- [Phantom 钱包](https://phantom.app/)

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！
