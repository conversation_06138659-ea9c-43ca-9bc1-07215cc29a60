import React, { useMemo } from 'react';
import { ConnectionProvider, WalletProvider as SolanaWalletProvider } from '@solana/wallet-adapter-react';
import { WalletModalProvider } from '@solana/wallet-adapter-react-ui';
import { clusterApiUrl } from '@solana/web3.js';

// 导入钱包适配器样式
import '@solana/wallet-adapter-react-ui/styles.css';

const WalletProvider = ({ children }) => {
  // 使用 devnet 进行测试
  const network = 'devnet';
  const endpoint = useMemo(() => clusterApiUrl(network), [network]);

  // 使用空数组，让钱包适配器自动检测标准钱包
  const wallets = useMemo(() => [], []);

  return (
    <ConnectionProvider endpoint={endpoint}>
      <SolanaWalletProvider wallets={wallets} autoConnect>
        <WalletModalProvider>
          {children}
        </WalletModalProvider>
      </SolanaWalletProvider>
    </ConnectionProvider>
  );
};

export default WalletProvider;
