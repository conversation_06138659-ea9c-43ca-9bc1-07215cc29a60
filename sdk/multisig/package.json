{"name": "@sqds/multisig", "version": "2.1.4", "description": "SDK for Squads Multisig Program v4", "main": "lib/index.js", "module": "lib/index.mjs", "types": "lib/index.d.ts", "exports": {".": {"types": "./lib/index.d.ts", "require": "./lib/index.js", "import": "./lib/index.mjs", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "license": "MIT", "sideEffects": false, "scripts": {"build:js": "tsup src/index.ts --format esm,cjs --outDir lib", "build:types": "tsc --emitDeclarationOnly", "build": "yarn build:js && yarn build:types", "generate": "yarn solita", "ts": "tsc --noEmit", "prepare:canary": "yarn build && npm version --prerelease --preid=canary"}, "dependencies": {"@metaplex-foundation/beet": "0.7.1", "@metaplex-foundation/beet-solana": "0.4.0", "@metaplex-foundation/cusper": "^0.0.2", "@solana/spl-token": "^0.3.6", "@solana/web3.js": "^1.70.3", "@types/bn.js": "^5.1.1", "assert": "^2.0.0", "bn.js": "^5.2.1", "buffer": "6.0.3", "invariant": "2.2.4"}, "devDependencies": {"@metaplex-foundation/solita": "0.20.0", "@types/invariant": "2.2.35", "@types/node": "18.11.17", "tsup": "^8.0.2", "typedoc": "^0.25.7", "typescript": "*"}, "engines": {"node": ">=14"}}