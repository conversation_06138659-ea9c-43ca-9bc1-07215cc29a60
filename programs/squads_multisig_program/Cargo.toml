[package]
name = "squads-multisig-program"
version = "2.1.0"
description = "Squads Multisig Program V4"
edition = "2021"
license-file = "../../LICENSE"

[lib]
crate-type = ["cdylib", "lib"]
name = "squads_multisig_program"

[features]
default = ["custom-heap"]
custom-heap = []
no-entrypoint = []
no-idl = []
no-log-ix-name = []
cpi = ["no-entrypoint"]
testing = []

[dependencies]
anchor-lang = { version = "=0.29.0", features = ["allow-missing-optionals"] }
anchor-spl = { version="=0.29.0", features=["token"] }
solana-program = "1.17.4"
solana-security-txt = "1.1.1"