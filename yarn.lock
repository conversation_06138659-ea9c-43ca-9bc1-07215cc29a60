# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@babel/runtime@^7.12.5", "@babel/runtime@^7.17.2":
  version "7.20.7"
  dependencies:
    regenerator-runtime "^0.13.11"

"@cspotcode/source-map-support@^0.8.0":
  version "0.8.1"
  dependencies:
    "@jridgewell/trace-mapping" "0.3.9"

"@esbuild/darwin-arm64@0.19.12":
  version "0.19.12"

"@isaacs/cliui@^8.0.2":
  version "8.0.2"
  dependencies:
    string-width "^5.1.2"
    string-width-cjs "npm:string-width@^4.2.0"
    strip-ansi "^7.0.1"
    strip-ansi-cjs "npm:strip-ansi@^6.0.1"
    wrap-ansi "^8.1.0"
    wrap-ansi-cjs "npm:wrap-ansi@^7.0.0"

"@jridgewell/gen-mapping@^0.3.2":
  version "0.3.12"
  resolved "https://repo.huaweicloud.com/repository/npm/@jridgewell/gen-mapping/-/gen-mapping-0.3.12.tgz"
  integrity sha512-OuLGC46TjB5BbN1dH8JULVVZY4WTdkF7tV9Ys6wLL1rubZnCMstOhNHueU5bLCrnRuDhKPDM4g6sw4Bel5Gzqg==
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.5.0"
    "@jridgewell/trace-mapping" "^0.3.24"

"@jridgewell/resolve-uri@^3.0.3":
  version "3.1.0"

"@jridgewell/resolve-uri@^3.1.0":
  version "3.1.2"

"@jridgewell/sourcemap-codec@^1.4.10", "@jridgewell/sourcemap-codec@^1.4.14", "@jridgewell/sourcemap-codec@^1.5.0":
  version "1.5.4"
  resolved "https://repo.huaweicloud.com/repository/npm/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.4.tgz"
  integrity sha512-VT2+G1VQs/9oz078bLrYbecdZKs912zQlkelYpuf+SXF+QvZDYJlbx/LSx+meSAwdDFnF8FVXW92AVjjkVmgFw==

"@jridgewell/trace-mapping@^0.3.24":
  version "0.3.25"
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@jridgewell/trace-mapping@0.3.9":
  version "0.3.9"
  dependencies:
    "@jridgewell/resolve-uri" "^3.0.3"
    "@jridgewell/sourcemap-codec" "^1.4.10"

"@metaplex-foundation/beet-solana@^0.3.1":
  version "0.3.1"
  dependencies:
    "@metaplex-foundation/beet" ">=0.1.0"
    "@solana/web3.js" "^1.56.2"
    bs58 "^5.0.0"
    debug "^4.3.4"

"@metaplex-foundation/beet-solana@0.4.0":
  version "0.4.0"
  dependencies:
    "@metaplex-foundation/beet" ">=0.1.0"
    "@solana/web3.js" "^1.56.2"
    bs58 "^5.0.0"
    debug "^4.3.4"

"@metaplex-foundation/beet@^0.7.1", "@metaplex-foundation/beet@>=0.1.0", "@metaplex-foundation/beet@0.7.1":
  version "0.7.1"
  dependencies:
    ansicolors "^0.3.2"
    bn.js "^5.2.0"
    debug "^4.3.3"

"@metaplex-foundation/cusper@^0.0.2":
  version "0.0.2"

"@metaplex-foundation/rustbin@^0.3.0":
  version "0.3.1"
  dependencies:
    debug "^4.3.3"
    semver "^7.3.7"
    text-table "^0.2.0"
    toml "^3.0.0"

"@metaplex-foundation/solita@0.20.0":
  version "0.20.0"
  dependencies:
    "@metaplex-foundation/beet" "^0.7.1"
    "@metaplex-foundation/beet-solana" "^0.3.1"
    "@metaplex-foundation/rustbin" "^0.3.0"
    "@solana/web3.js" "^1.56.2"
    ansi-colors "^4.1.3"
    camelcase "^6.2.1"
    debug "^4.3.3"
    js-sha256 "^0.9.0"
    prettier "^2.5.1"
    snake-case "^3.0.4"
    spok "^1.4.3"

"@noble/ed25519@^1.7.0":
  version "1.7.1"

"@noble/hashes@^1.1.2":
  version "1.1.5"

"@noble/secp256k1@^1.6.3":
  version "1.7.0"

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@^2.0.2", "@nodelib/fs.stat@2.0.5":
  version "2.0.5"

"@nodelib/fs.walk@^1.2.3":
  version "1.2.8"
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@pkgjs/parseargs@^0.11.0":
  version "0.11.0"

"@rollup/rollup-darwin-arm64@4.12.1":
  version "4.12.1"

"@solana/buffer-layout-utils@^0.2.0":
  version "0.2.0"
  dependencies:
    "@solana/buffer-layout" "^4.0.0"
    "@solana/web3.js" "^1.32.0"
    bigint-buffer "^1.1.5"
    bignumber.js "^9.0.1"

"@solana/buffer-layout@^4.0.0":
  version "4.0.1"
  dependencies:
    buffer "~6.0.3"

"@solana/spl-memo@^0.2.3":
  version "0.2.3"
  dependencies:
    buffer "^6.0.3"

"@solana/spl-token@*", "@solana/spl-token@^0.3.6":
  version "0.3.6"
  dependencies:
    "@solana/buffer-layout" "^4.0.0"
    "@solana/buffer-layout-utils" "^0.2.0"
    buffer "^6.0.3"

"@solana/web3.js@^1.20.0", "@solana/web3.js@^1.32.0", "@solana/web3.js@^1.47.4", "@solana/web3.js@^1.56.2", "@solana/web3.js@^1.70.3":
  version "1.70.3"
  dependencies:
    "@babel/runtime" "^7.12.5"
    "@noble/ed25519" "^1.7.0"
    "@noble/hashes" "^1.1.2"
    "@noble/secp256k1" "^1.6.3"
    "@solana/buffer-layout" "^4.0.0"
    agentkeepalive "^4.2.1"
    bigint-buffer "^1.1.5"
    bn.js "^5.0.0"
    borsh "^0.7.0"
    bs58 "^4.0.1"
    buffer "6.0.1"
    fast-stable-stringify "^1.0.0"
    jayson "^3.4.4"
    node-fetch "2"
    rpc-websockets "^7.5.0"
    superstruct "^0.14.2"

"@sqds/multisig@^2.1.4", "@sqds/multisig@file:/Users/<USER>/Me/work/3_wallet/coin/26-sol/2_code/squads-github/v4/sdk/multisig":
  version "2.1.4"
  resolved "file:sdk/multisig"
  dependencies:
    "@metaplex-foundation/beet" "0.7.1"
    "@metaplex-foundation/beet-solana" "0.4.0"
    "@metaplex-foundation/cusper" "^0.0.2"
    "@solana/spl-token" "^0.3.6"
    "@solana/web3.js" "^1.70.3"
    "@types/bn.js" "^5.1.1"
    assert "^2.0.0"
    bn.js "^5.2.1"
    buffer "6.0.3"
    invariant "2.2.4"

"@tsconfig/node10@^1.0.7":
  version "1.0.9"

"@tsconfig/node12@^1.0.7":
  version "1.0.11"

"@tsconfig/node14@^1.0.0":
  version "1.0.3"

"@tsconfig/node16@^1.0.2":
  version "1.0.3"

"@types/bn.js@^5.1.1":
  version "5.1.1"
  dependencies:
    "@types/node" "*"

"@types/bn.js@5.1.0":
  version "5.1.0"
  dependencies:
    "@types/node" "*"

"@types/connect@^3.4.33":
  version "3.4.35"
  dependencies:
    "@types/node" "*"

"@types/estree@1.0.5":
  version "1.0.5"

"@types/invariant@2.2.35":
  version "2.2.35"

"@types/mocha@10.0.1":
  version "10.0.1"

"@types/node-fetch@2.6.2":
  version "2.6.2"
  dependencies:
    "@types/node" "*"
    form-data "^3.0.0"

"@types/node@*", "@types/node@18.11.17":
  version "18.11.17"

"@types/node@^12.12.54":
  version "12.20.55"

"@types/ws@^7.4.4":
  version "7.4.7"
  dependencies:
    "@types/node" "*"

acorn-walk@^8.1.1:
  version "8.2.0"

acorn@^8.4.1:
  version "8.8.1"

agentkeepalive@^4.2.1:
  version "4.2.1"
  dependencies:
    debug "^4.1.0"
    depd "^1.1.2"
    humanize-ms "^1.2.1"

ansi-colors@^4.1.3:
  version "4.1.3"

ansi-colors@4.1.1:
  version "4.1.1"

ansi-regex@^5.0.1:
  version "5.0.1"

ansi-regex@^6.0.1:
  version "6.0.1"

ansi-sequence-parser@^1.1.0:
  version "1.1.1"

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.3.0"
  dependencies:
    color-convert "^2.0.1"

ansi-styles@^6.1.0:
  version "6.2.1"

ansicolors@^0.3.2, ansicolors@~0.3.2:
  version "0.3.2"

any-promise@^1.0.0:
  version "1.3.0"

anymatch@~3.1.2:
  version "3.1.3"
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

arg@^4.1.0:
  version "4.1.3"

argparse@^2.0.1:
  version "2.0.1"

array-union@^2.1.0:
  version "2.1.0"

assert@^2.0.0:
  version "2.0.0"
  dependencies:
    es6-object-assign "^1.1.0"
    is-nan "^1.2.1"
    object-is "^1.0.1"
    util "^0.12.0"

asynckit@^0.4.0:
  version "0.4.0"

available-typed-arrays@^1.0.5:
  version "1.0.5"

balanced-match@^1.0.0:
  version "1.0.2"

base-x@^3.0.2:
  version "3.0.9"
  dependencies:
    safe-buffer "^5.0.1"

base-x@^4.0.0:
  version "4.0.0"

base64-js@^1.3.1:
  version "1.5.1"

bigint-buffer@^1.1.5:
  version "1.1.5"
  dependencies:
    bindings "^1.3.0"

bignumber.js@^9.0.1:
  version "9.1.1"

binary-extensions@^2.0.0:
  version "2.2.0"

bindings@^1.3.0:
  version "1.5.0"
  dependencies:
    file-uri-to-path "1.0.0"

bn.js@^5.0.0, bn.js@^5.2.0, bn.js@^5.2.1:
  version "5.2.1"

borsh@^0.7.0:
  version "0.7.0"
  dependencies:
    bn.js "^5.2.0"
    bs58 "^4.0.0"
    text-encoding-utf-8 "^1.0.2"

brace-expansion@^1.1.7:
  version "1.1.11"
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

brace-expansion@^2.0.1:
  version "2.0.1"
  dependencies:
    balanced-match "^1.0.0"

braces@^3.0.2, braces@~3.0.2:
  version "3.0.2"
  dependencies:
    fill-range "^7.0.1"

browser-stdout@1.3.1:
  version "1.3.1"

bs58@^4.0.0, bs58@^4.0.1:
  version "4.0.1"
  dependencies:
    base-x "^3.0.2"

bs58@^5.0.0:
  version "5.0.0"
  dependencies:
    base-x "^4.0.0"

buffer@^6.0.3, buffer@~6.0.3, buffer@6.0.3:
  version "6.0.3"
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.2.1"

buffer@6.0.1:
  version "6.0.1"
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.2.1"

bufferutil@^4.0.1:
  version "4.0.7"
  dependencies:
    node-gyp-build "^4.3.0"

bundle-require@^4.0.0:
  version "4.0.2"
  dependencies:
    load-tsconfig "^0.2.3"

cac@^6.7.12:
  version "6.7.14"

call-bind@^1.0.0, call-bind@^1.0.2:
  version "1.0.2"
  dependencies:
    function-bind "^1.1.1"
    get-intrinsic "^1.0.2"

camelcase@^6.0.0, camelcase@^6.2.1:
  version "6.3.0"

chalk@^4.1.0:
  version "4.1.2"
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chokidar@^3.5.1:
  version "3.6.0"
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

chokidar@3.5.3:
  version "3.5.3"
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

cliui@^7.0.2:
  version "7.0.4"
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.0"
    wrap-ansi "^7.0.0"

color-convert@^2.0.1:
  version "2.0.1"
  dependencies:
    color-name "~1.1.4"

color-name@~1.1.4:
  version "1.1.4"

combined-stream@^1.0.8:
  version "1.0.8"
  dependencies:
    delayed-stream "~1.0.0"

commander@^2.20.3:
  version "2.20.3"

commander@^4.0.0:
  version "4.1.1"

concat-map@0.0.1:
  version "0.0.1"

create-require@^1.1.0:
  version "1.1.1"

cross-spawn@^7.0.0, cross-spawn@^7.0.3:
  version "7.0.3"
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

debug@^4.1.0, debug@^4.3.1, debug@^4.3.3, debug@^4.3.4, debug@4.3.4:
  version "4.3.4"
  dependencies:
    ms "2.1.2"

decamelize@^4.0.0:
  version "4.0.0"

define-properties@^1.1.3:
  version "1.1.4"
  dependencies:
    has-property-descriptors "^1.0.0"
    object-keys "^1.1.1"

delay@^5.0.0:
  version "5.0.0"

delayed-stream@~1.0.0:
  version "1.0.0"

depd@^1.1.2:
  version "1.1.2"

diff@^4.0.1:
  version "4.0.2"

diff@5.0.0:
  version "5.0.0"

dir-glob@^3.0.1:
  version "3.0.1"
  dependencies:
    path-type "^4.0.0"

dot-case@^3.0.4:
  version "3.0.4"
  dependencies:
    no-case "^3.0.4"
    tslib "^2.0.3"

eastasianwidth@^0.2.0:
  version "0.2.0"

emoji-regex@^8.0.0:
  version "8.0.0"

emoji-regex@^9.2.2:
  version "9.2.2"

encoding@^0.1.0:
  version "0.1.13"
  dependencies:
    iconv-lite "^0.6.2"

es6-object-assign@^1.1.0:
  version "1.1.0"

es6-promise@^4.0.3:
  version "4.2.8"

es6-promisify@^5.0.0:
  version "5.0.0"
  dependencies:
    es6-promise "^4.0.3"

esbuild@^0.19.2, esbuild@>=0.17:
  version "0.19.12"
  optionalDependencies:
    "@esbuild/aix-ppc64" "0.19.12"
    "@esbuild/android-arm" "0.19.12"
    "@esbuild/android-arm64" "0.19.12"
    "@esbuild/android-x64" "0.19.12"
    "@esbuild/darwin-arm64" "0.19.12"
    "@esbuild/darwin-x64" "0.19.12"
    "@esbuild/freebsd-arm64" "0.19.12"
    "@esbuild/freebsd-x64" "0.19.12"
    "@esbuild/linux-arm" "0.19.12"
    "@esbuild/linux-arm64" "0.19.12"
    "@esbuild/linux-ia32" "0.19.12"
    "@esbuild/linux-loong64" "0.19.12"
    "@esbuild/linux-mips64el" "0.19.12"
    "@esbuild/linux-ppc64" "0.19.12"
    "@esbuild/linux-riscv64" "0.19.12"
    "@esbuild/linux-s390x" "0.19.12"
    "@esbuild/linux-x64" "0.19.12"
    "@esbuild/netbsd-x64" "0.19.12"
    "@esbuild/openbsd-x64" "0.19.12"
    "@esbuild/sunos-x64" "0.19.12"
    "@esbuild/win32-arm64" "0.19.12"
    "@esbuild/win32-ia32" "0.19.12"
    "@esbuild/win32-x64" "0.19.12"

escalade@^3.1.1:
  version "3.2.0"
  resolved "https://repo.huaweicloud.com/repository/npm/escalade/-/escalade-3.2.0.tgz"
  integrity sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==

escape-string-regexp@4.0.0:
  version "4.0.0"

eventemitter3@^4.0.7:
  version "4.0.7"

execa@^5.0.0:
  version "5.1.1"
  dependencies:
    cross-spawn "^7.0.3"
    get-stream "^6.0.0"
    human-signals "^2.1.0"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.1"
    onetime "^5.1.2"
    signal-exit "^3.0.3"
    strip-final-newline "^2.0.0"

eyes@^0.1.8:
  version "0.1.8"

fast-glob@^3.2.9:
  version "3.3.2"
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.4"

fast-stable-stringify@^1.0.0:
  version "1.0.0"

fastq@^1.6.0:
  version "1.17.1"
  dependencies:
    reusify "^1.0.4"

file-uri-to-path@1.0.0:
  version "1.0.0"

fill-range@^7.0.1:
  version "7.0.1"
  dependencies:
    to-regex-range "^5.0.1"

find-up@5.0.0:
  version "5.0.0"
  dependencies:
    locate-path "^6.0.0"
    path-exists "^4.0.0"

flat@^5.0.2:
  version "5.0.2"

for-each@^0.3.3:
  version "0.3.3"
  dependencies:
    is-callable "^1.1.3"

foreground-child@^3.1.0:
  version "3.1.1"
  dependencies:
    cross-spawn "^7.0.0"
    signal-exit "^4.0.1"

form-data@^3.0.0:
  version "3.0.1"
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    mime-types "^2.1.12"

fs.realpath@^1.0.0:
  version "1.0.0"

fsevents@~2.3.2:
  version "2.3.2"

function-bind@^1.1.1:
  version "1.1.2"
  resolved "https://repo.huaweicloud.com/repository/npm/function-bind/-/function-bind-1.1.2.tgz"
  integrity sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==

get-caller-file@^2.0.5:
  version "2.0.5"

get-intrinsic@^1.0.2, get-intrinsic@^1.1.1, get-intrinsic@^1.1.3:
  version "1.1.3"
  dependencies:
    function-bind "^1.1.1"
    has "^1.0.3"
    has-symbols "^1.0.3"

get-stream@^6.0.0:
  version "6.0.1"

glob-parent@^5.1.2, glob-parent@~5.1.2:
  version "5.1.2"
  dependencies:
    is-glob "^4.0.1"

glob@^10.3.10:
  version "10.3.10"
  dependencies:
    foreground-child "^3.1.0"
    jackspeak "^2.3.5"
    minimatch "^9.0.1"
    minipass "^5.0.0 || ^6.0.2 || ^7.0.0"
    path-scurry "^1.10.1"

glob@7.2.0:
  version "7.2.0"
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.4"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

globby@^11.0.3:
  version "11.1.0"
  dependencies:
    array-union "^2.1.0"
    dir-glob "^3.0.1"
    fast-glob "^3.2.9"
    ignore "^5.2.0"
    merge2 "^1.4.1"
    slash "^3.0.0"

gopd@^1.0.1:
  version "1.0.1"
  dependencies:
    get-intrinsic "^1.1.3"

has-flag@^4.0.0:
  version "4.0.0"

has-property-descriptors@^1.0.0:
  version "1.0.0"
  dependencies:
    get-intrinsic "^1.1.1"

has-symbols@^1.0.2, has-symbols@^1.0.3:
  version "1.0.3"

has-tostringtag@^1.0.0:
  version "1.0.0"
  dependencies:
    has-symbols "^1.0.2"

has@^1.0.3:
  version "1.0.3"
  dependencies:
    function-bind "^1.1.1"

he@1.2.0:
  version "1.2.0"

human-signals@^2.1.0:
  version "2.1.0"

humanize-ms@^1.2.1:
  version "1.2.1"
  dependencies:
    ms "^2.0.0"

iconv-lite@^0.6.2:
  version "0.6.3"
  dependencies:
    safer-buffer ">= 2.1.2 < 3.0.0"

ieee754@^1.2.1:
  version "1.2.1"

ignore@^5.2.0:
  version "5.3.1"

inflight@^1.0.4:
  version "1.0.6"
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@^2.0.3, inherits@2:
  version "2.0.4"

invariant@2.2.4:
  version "2.2.4"
  dependencies:
    loose-envify "^1.0.0"

is-arguments@^1.0.4:
  version "1.1.1"
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-binary-path@~2.1.0:
  version "2.1.0"
  dependencies:
    binary-extensions "^2.0.0"

is-callable@^1.1.3:
  version "1.2.7"

is-extglob@^2.1.1:
  version "2.1.1"

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"

is-generator-function@^1.0.7:
  version "1.0.10"
  dependencies:
    has-tostringtag "^1.0.0"

is-glob@^4.0.1, is-glob@~4.0.1:
  version "4.0.3"
  dependencies:
    is-extglob "^2.1.1"

is-nan@^1.2.1:
  version "1.3.2"
  dependencies:
    call-bind "^1.0.0"
    define-properties "^1.1.3"

is-number@^7.0.0:
  version "7.0.0"

is-plain-obj@^2.1.0:
  version "2.1.0"

is-stream@^2.0.0:
  version "2.0.1"

is-typed-array@^1.1.10, is-typed-array@^1.1.3:
  version "1.1.10"
  dependencies:
    available-typed-arrays "^1.0.5"
    call-bind "^1.0.2"
    for-each "^0.3.3"
    gopd "^1.0.1"
    has-tostringtag "^1.0.0"

is-unicode-supported@^0.1.0:
  version "0.1.0"

isexe@^2.0.0:
  version "2.0.0"

isomorphic-ws@^4.0.1:
  version "4.0.1"

jackspeak@^2.3.5:
  version "2.3.6"
  dependencies:
    "@isaacs/cliui" "^8.0.2"
  optionalDependencies:
    "@pkgjs/parseargs" "^0.11.0"

jayson@^3.4.4:
  version "3.7.0"
  dependencies:
    "@types/connect" "^3.4.33"
    "@types/node" "^12.12.54"
    "@types/ws" "^7.4.4"
    commander "^2.20.3"
    delay "^5.0.0"
    es6-promisify "^5.0.0"
    eyes "^0.1.8"
    isomorphic-ws "^4.0.1"
    json-stringify-safe "^5.0.1"
    JSONStream "^1.3.5"
    lodash "^4.17.20"
    uuid "^8.3.2"
    ws "^7.4.5"

joycon@^3.0.1:
  version "3.1.1"

js-sha256@^0.9.0:
  version "0.9.0"

"js-tokens@^3.0.0 || ^4.0.0":
  version "4.0.0"

js-yaml@4.1.0:
  version "4.1.0"
  dependencies:
    argparse "^2.0.1"

json-stringify-safe@^5.0.1:
  version "5.0.1"

jsonc-parser@^3.2.0:
  version "3.2.1"

jsonparse@^1.2.0:
  version "1.3.1"

JSONStream@^1.3.5:
  version "1.3.5"
  dependencies:
    jsonparse "^1.2.0"
    through ">=2.2.7 <3"

lilconfig@^3.0.0:
  version "3.1.1"

lines-and-columns@^1.1.6:
  version "1.2.4"

load-tsconfig@^0.2.3:
  version "0.2.5"

locate-path@^6.0.0:
  version "6.0.0"
  dependencies:
    p-locate "^5.0.0"

lodash.sortby@^4.7.0:
  version "4.7.0"

lodash@^4.17.20:
  version "4.17.21"

log-symbols@4.1.0:
  version "4.1.0"
  dependencies:
    chalk "^4.1.0"
    is-unicode-supported "^0.1.0"

loose-envify@^1.0.0:
  version "1.4.0"
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

lower-case@^2.0.2:
  version "2.0.2"
  dependencies:
    tslib "^2.0.3"

lru-cache@^6.0.0:
  version "6.0.0"
  dependencies:
    yallist "^4.0.0"

"lru-cache@^9.1.1 || ^10.0.0":
  version "10.2.0"

lunr@^2.3.9:
  version "2.3.9"

make-error@^1.1.1:
  version "1.3.6"

marked@^4.3.0:
  version "4.3.0"

merge-stream@^2.0.0:
  version "2.0.0"

merge2@^1.3.0, merge2@^1.4.1:
  version "1.4.1"

micromatch@^4.0.4:
  version "4.0.5"
  dependencies:
    braces "^3.0.2"
    picomatch "^2.3.1"

mime-db@1.52.0:
  version "1.52.0"

mime-types@^2.1.12:
  version "2.1.35"
  dependencies:
    mime-db "1.52.0"

mimic-fn@^2.1.0:
  version "2.1.0"

minimatch@^3.0.4:
  version "3.1.2"
  resolved "https://repo.huaweicloud.com/repository/npm/minimatch/-/minimatch-3.1.2.tgz"
  integrity sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^9.0.1, minimatch@^9.0.3:
  version "9.0.3"
  dependencies:
    brace-expansion "^2.0.1"

minimatch@5.0.1:
  version "5.0.1"
  dependencies:
    brace-expansion "^2.0.1"

"minipass@^5.0.0 || ^6.0.2 || ^7.0.0":
  version "7.0.4"

mocha@10.2.0:
  version "10.2.0"
  dependencies:
    ansi-colors "4.1.1"
    browser-stdout "1.3.1"
    chokidar "3.5.3"
    debug "4.3.4"
    diff "5.0.0"
    escape-string-regexp "4.0.0"
    find-up "5.0.0"
    glob "7.2.0"
    he "1.2.0"
    js-yaml "4.1.0"
    log-symbols "4.1.0"
    minimatch "5.0.1"
    ms "2.1.3"
    nanoid "3.3.3"
    serialize-javascript "6.0.0"
    strip-json-comments "3.1.1"
    supports-color "8.1.1"
    workerpool "6.2.1"
    yargs "16.2.0"
    yargs-parser "20.2.4"
    yargs-unparser "2.0.0"

ms@^2.0.0, ms@2.1.3:
  version "2.1.3"

ms@2.1.2:
  version "2.1.2"

mz@^2.7.0:
  version "2.7.0"
  dependencies:
    any-promise "^1.0.0"
    object-assign "^4.0.1"
    thenify-all "^1.0.0"

nanoid@3.3.3:
  version "3.3.3"

no-case@^3.0.4:
  version "3.0.4"
  dependencies:
    lower-case "^2.0.2"
    tslib "^2.0.3"

node-fetch@2:
  version "2.6.7"
  dependencies:
    whatwg-url "^5.0.0"

node-gyp-build@^4.3.0:
  version "4.5.0"

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"

npm-run-path@^4.0.1:
  version "4.0.1"
  dependencies:
    path-key "^3.0.0"

object-assign@^4.0.1:
  version "4.1.1"

object-is@^1.0.1:
  version "1.1.5"
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"

object-keys@^1.1.1:
  version "1.1.1"

once@^1.3.0:
  version "1.4.0"
  dependencies:
    wrappy "1"

onetime@^5.1.2:
  version "5.1.2"
  dependencies:
    mimic-fn "^2.1.0"

p-limit@^3.0.2:
  version "3.1.0"
  dependencies:
    yocto-queue "^0.1.0"

p-locate@^5.0.0:
  version "5.0.0"
  dependencies:
    p-limit "^3.0.2"

path-exists@^4.0.0:
  version "4.0.0"

path-is-absolute@^1.0.0:
  version "1.0.1"

path-key@^3.0.0, path-key@^3.1.0:
  version "3.1.1"

path-scurry@^1.10.1:
  version "1.10.1"
  dependencies:
    lru-cache "^9.1.1 || ^10.0.0"
    minipass "^5.0.0 || ^6.0.2 || ^7.0.0"

path-type@^4.0.0:
  version "4.0.0"

picomatch@^2.0.4, picomatch@^2.2.1, picomatch@^2.3.1:
  version "2.3.1"

pirates@^4.0.1:
  version "4.0.6"

postcss-load-config@^4.0.1:
  version "4.0.2"
  dependencies:
    lilconfig "^3.0.0"
    yaml "^2.3.4"

prettier@^2.5.1:
  version "2.8.3"

prettier@2.6.2:
  version "2.6.2"

punycode@^2.1.0:
  version "2.3.1"

queue-microtask@^1.2.2:
  version "1.2.3"

randombytes@^2.1.0:
  version "2.1.0"
  dependencies:
    safe-buffer "^5.1.0"

readdirp@~3.6.0:
  version "3.6.0"
  dependencies:
    picomatch "^2.2.1"

regenerator-runtime@^0.13.11:
  version "0.13.11"

require-directory@^2.1.1:
  version "2.1.1"

resolve-from@^5.0.0:
  version "5.0.0"

reusify@^1.0.4:
  version "1.0.4"

rollup@^4.0.2:
  version "4.12.1"
  dependencies:
    "@types/estree" "1.0.5"
  optionalDependencies:
    "@rollup/rollup-android-arm-eabi" "4.12.1"
    "@rollup/rollup-android-arm64" "4.12.1"
    "@rollup/rollup-darwin-arm64" "4.12.1"
    "@rollup/rollup-darwin-x64" "4.12.1"
    "@rollup/rollup-linux-arm-gnueabihf" "4.12.1"
    "@rollup/rollup-linux-arm64-gnu" "4.12.1"
    "@rollup/rollup-linux-arm64-musl" "4.12.1"
    "@rollup/rollup-linux-riscv64-gnu" "4.12.1"
    "@rollup/rollup-linux-x64-gnu" "4.12.1"
    "@rollup/rollup-linux-x64-musl" "4.12.1"
    "@rollup/rollup-win32-arm64-msvc" "4.12.1"
    "@rollup/rollup-win32-ia32-msvc" "4.12.1"
    "@rollup/rollup-win32-x64-msvc" "4.12.1"
    fsevents "~2.3.2"

rpc-websockets@^7.5.0:
  version "7.5.0"
  dependencies:
    "@babel/runtime" "^7.17.2"
    eventemitter3 "^4.0.7"
    uuid "^8.3.2"
    ws "^8.5.0"
  optionalDependencies:
    bufferutil "^4.0.1"
    utf-8-validate "^5.0.2"

run-parallel@^1.1.9:
  version "1.2.0"
  dependencies:
    queue-microtask "^1.2.2"

safe-buffer@^5.0.1, safe-buffer@^5.1.0:
  version "5.2.1"

"safer-buffer@>= 2.1.2 < 3.0.0":
  version "2.1.2"

semver@^7.3.7:
  version "7.3.8"
  dependencies:
    lru-cache "^6.0.0"

serialize-javascript@6.0.0:
  version "6.0.0"
  dependencies:
    randombytes "^2.1.0"

shebang-command@^2.0.0:
  version "2.0.0"
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^3.0.0:
  version "3.0.0"

shiki@^0.14.7:
  version "0.14.7"
  dependencies:
    ansi-sequence-parser "^1.1.0"
    jsonc-parser "^3.2.0"
    vscode-oniguruma "^1.7.0"
    vscode-textmate "^8.0.0"

signal-exit@^3.0.3:
  version "3.0.7"

signal-exit@^4.0.1:
  version "4.1.0"

slash@^3.0.0:
  version "3.0.0"

snake-case@^3.0.4:
  version "3.0.4"
  dependencies:
    dot-case "^3.0.4"
    tslib "^2.0.3"

source-map@0.8.0-beta.0:
  version "0.8.0-beta.0"
  dependencies:
    whatwg-url "^7.0.0"

spok@^1.4.3:
  version "1.4.3"
  dependencies:
    ansicolors "~0.3.2"

"string-width-cjs@npm:string-width@^4.2.0":
  version "4.2.3"
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^4.1.0, string-width@^4.2.0:
  version "4.2.3"
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^5.0.1, string-width@^5.1.2:
  version "5.1.2"
  dependencies:
    eastasianwidth "^0.2.0"
    emoji-regex "^9.2.2"
    strip-ansi "^7.0.1"

"strip-ansi-cjs@npm:strip-ansi@^6.0.1":
  version "6.0.1"
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^7.0.1:
  version "7.1.0"
  dependencies:
    ansi-regex "^6.0.1"

strip-final-newline@^2.0.0:
  version "2.0.0"

strip-json-comments@3.1.1:
  version "3.1.1"

sucrase@^3.20.3:
  version "3.35.0"
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.2"
    commander "^4.0.0"
    glob "^10.3.10"
    lines-and-columns "^1.1.6"
    mz "^2.7.0"
    pirates "^4.0.1"
    ts-interface-checker "^0.1.9"

superstruct@^0.14.2:
  version "0.14.2"

supports-color@^7.1.0:
  version "7.2.0"
  resolved "https://repo.huaweicloud.com/repository/npm/supports-color/-/supports-color-7.2.0.tgz"
  integrity sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==
  dependencies:
    has-flag "^4.0.0"

supports-color@8.1.1:
  version "8.1.1"
  dependencies:
    has-flag "^4.0.0"

text-encoding-utf-8@^1.0.2:
  version "1.0.2"

text-table@^0.2.0:
  version "0.2.0"

thenify-all@^1.0.0:
  version "1.6.0"
  dependencies:
    thenify ">= 3.1.0 < 4"

"thenify@>= 3.1.0 < 4":
  version "3.3.1"
  dependencies:
    any-promise "^1.0.0"

"through@>=2.2.7 <3":
  version "2.3.8"

to-regex-range@^5.0.1:
  version "5.0.1"
  dependencies:
    is-number "^7.0.0"

toml@^3.0.0:
  version "3.0.0"

tr46@^1.0.1:
  version "1.0.1"
  dependencies:
    punycode "^2.1.0"

tr46@~0.0.3:
  version "0.0.3"

tree-kill@^1.2.2:
  version "1.2.2"

ts-interface-checker@^0.1.9:
  version "0.1.13"

ts-node@>=9.0.0, ts-node@10.9.1:
  version "10.9.1"
  dependencies:
    "@cspotcode/source-map-support" "^0.8.0"
    "@tsconfig/node10" "^1.0.7"
    "@tsconfig/node12" "^1.0.7"
    "@tsconfig/node14" "^1.0.0"
    "@tsconfig/node16" "^1.0.2"
    acorn "^8.4.1"
    acorn-walk "^8.1.1"
    arg "^4.1.0"
    create-require "^1.1.0"
    diff "^4.0.1"
    make-error "^1.1.1"
    v8-compile-cache-lib "^3.0.1"
    yn "3.1.1"

tslib@^2.0.3:
  version "2.5.0"

tsup@^8.0.2:
  version "8.0.2"
  dependencies:
    bundle-require "^4.0.0"
    cac "^6.7.12"
    chokidar "^3.5.1"
    debug "^4.3.1"
    esbuild "^0.19.2"
    execa "^5.0.0"
    globby "^11.0.3"
    joycon "^3.0.1"
    postcss-load-config "^4.0.1"
    resolve-from "^5.0.0"
    rollup "^4.0.2"
    source-map "0.8.0-beta.0"
    sucrase "^3.20.3"
    tree-kill "^1.2.2"

turbo-darwin-arm64@1.6.3:
  version "1.6.3"

turbo@1.6.3:
  version "1.6.3"
  optionalDependencies:
    turbo-darwin-64 "1.6.3"
    turbo-darwin-arm64 "1.6.3"
    turbo-linux-64 "1.6.3"
    turbo-linux-arm64 "1.6.3"
    turbo-windows-64 "1.6.3"
    turbo-windows-arm64 "1.6.3"

typedoc@^0.25.7:
  version "0.25.7"
  dependencies:
    lunr "^2.3.9"
    marked "^4.3.0"
    minimatch "^9.0.3"
    shiki "^0.14.7"

typescript@*, typescript@>=2.7, typescript@>=4.5.0, "typescript@4.6.x || 4.7.x || 4.8.x || 4.9.x || 5.0.x || 5.1.x || 5.2.x || 5.3.x":
  version "4.9.4"

utf-8-validate@^5.0.2:
  version "5.0.10"
  dependencies:
    node-gyp-build "^4.3.0"

util@^0.12.0:
  version "0.12.5"
  dependencies:
    inherits "^2.0.3"
    is-arguments "^1.0.4"
    is-generator-function "^1.0.7"
    is-typed-array "^1.1.3"
    which-typed-array "^1.1.2"

uuid@^8.3.2:
  version "8.3.2"

v8-compile-cache-lib@^3.0.1:
  version "3.0.1"

vscode-oniguruma@^1.7.0:
  version "1.7.0"

vscode-textmate@^8.0.0:
  version "8.0.0"

webidl-conversions@^3.0.0:
  version "3.0.1"

webidl-conversions@^4.0.2:
  version "4.0.2"

whatwg-url@^5.0.0:
  version "5.0.0"
  dependencies:
    tr46 "~0.0.3"
    webidl-conversions "^3.0.0"

whatwg-url@^7.0.0:
  version "7.1.0"
  dependencies:
    lodash.sortby "^4.7.0"
    tr46 "^1.0.1"
    webidl-conversions "^4.0.2"

which-typed-array@^1.1.2:
  version "1.1.9"
  dependencies:
    available-typed-arrays "^1.0.5"
    call-bind "^1.0.2"
    for-each "^0.3.3"
    gopd "^1.0.1"
    has-tostringtag "^1.0.0"
    is-typed-array "^1.1.10"

which@^2.0.1:
  version "2.0.2"
  dependencies:
    isexe "^2.0.0"

workerpool@6.2.1:
  version "6.2.1"

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0":
  version "7.0.0"
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^7.0.0:
  version "7.0.0"
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^8.1.0:
  version "8.1.0"
  dependencies:
    ansi-styles "^6.1.0"
    string-width "^5.0.1"
    strip-ansi "^7.0.1"

wrappy@1:
  version "1.0.2"

ws@*, ws@^7.4.5:
  version "7.5.9"

ws@^8.5.0:
  version "8.11.0"

y18n@^5.0.5:
  version "5.0.8"

yallist@^4.0.0:
  version "4.0.0"

yaml@^2.3.4:
  version "2.4.1"

yargs-parser@^20.2.2:
  version "20.2.9"

yargs-parser@20.2.4:
  version "20.2.4"

yargs-unparser@2.0.0:
  version "2.0.0"
  dependencies:
    camelcase "^6.0.0"
    decamelize "^4.0.0"
    flat "^5.0.2"
    is-plain-obj "^2.1.0"

yargs@16.2.0:
  version "16.2.0"
  dependencies:
    cliui "^7.0.2"
    escalade "^3.1.1"
    get-caller-file "^2.0.5"
    require-directory "^2.1.1"
    string-width "^4.2.0"
    y18n "^5.0.5"
    yargs-parser "^20.2.2"

yn@3.1.1:
  version "3.1.1"

yocto-queue@^0.1.0:
  version "0.1.0"
