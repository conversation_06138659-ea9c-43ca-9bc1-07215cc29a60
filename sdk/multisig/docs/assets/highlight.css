:root {
    --light-hl-0: #001080;
    --dark-hl-0: #9CDCFE;
    --light-hl-1: #000000;
    --dark-hl-1: #D4D4D4;
    --light-hl-2: #000000;
    --dark-hl-2: #C8C8C8;
    --light-hl-3: #008000;
    --dark-hl-3: #6A9955;
    --light-code-background: #FFFFFF;
    --dark-code-background: #1E1E1E;
}

@media (prefers-color-scheme: light) { :root {
    --hl-0: var(--light-hl-0);
    --hl-1: var(--light-hl-1);
    --hl-2: var(--light-hl-2);
    --hl-3: var(--light-hl-3);
    --code-background: var(--light-code-background);
} }

@media (prefers-color-scheme: dark) { :root {
    --hl-0: var(--dark-hl-0);
    --hl-1: var(--dark-hl-1);
    --hl-2: var(--dark-hl-2);
    --hl-3: var(--dark-hl-3);
    --code-background: var(--dark-code-background);
} }

:root[data-theme='light'] {
    --hl-0: var(--light-hl-0);
    --hl-1: var(--light-hl-1);
    --hl-2: var(--light-hl-2);
    --hl-3: var(--light-hl-3);
    --code-background: var(--light-code-background);
}

:root[data-theme='dark'] {
    --hl-0: var(--dark-hl-0);
    --hl-1: var(--dark-hl-1);
    --hl-2: var(--dark-hl-2);
    --hl-3: var(--dark-hl-3);
    --code-background: var(--dark-code-background);
}

.hl-0 { color: var(--hl-0); }
.hl-1 { color: var(--hl-1); }
.hl-2 { color: var(--hl-2); }
.hl-3 { color: var(--hl-3); }
pre, code { background: var(--code-background); }
