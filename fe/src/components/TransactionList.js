import React, { useState, useEffect } from 'react';
import { useWallet, useConnection } from '@solana/wallet-adapter-react';
import { PublicKey, Transaction } from '@solana/web3.js';
import * as multisig from '@sqds/multisig';
import { useMultisig } from '../hooks/useMultisig';
import toast from 'react-hot-toast';

const TransactionList = () => {
  const { publicKey, sendTransaction } = useWallet();
  const { connection } = useConnection();
  const { getMultisigPda, getProgramId } = useMultisig();
  
  const [transactions, setTransactions] = useState([]);
  const [multisigInfo, setMultisigInfo] = useState(null);
  const [loading, setLoading] = useState(false);
  const [actionLoading, setActionLoading] = useState({});

  // 加载交易列表
  const loadTransactions = async () => {
    const multisigPda = getMultisigPda();
    const programId = getProgramId();

    if (!multisigPda) {
      toast.error('请先配置多签地址');
      return;
    }

    setLoading(true);
    try {
      // 获取多签信息
      const info = await multisig.accounts.Multisig.fromAccountAddress(
        connection,
        multisigPda,
        undefined,
        programId
      );
      setMultisigInfo(info);

      // 获取最近的交易
      const txList = [];
      const currentIndex = Number(info.transactionIndex);
      
      // 获取最近 10 个交易
      for (let i = Math.max(1, currentIndex - 9); i <= currentIndex; i++) {
        try {
          const [transactionPda] = multisig.getTransactionPda({
            multisigPda,
            index: BigInt(i),
            programId,
          });

          const [proposalPda] = multisig.getProposalPda({
            multisigPda,
            transactionIndex: BigInt(i),
            programId,
          });

          // 获取交易信息
          const txInfo = await multisig.accounts.VaultTransaction.fromAccountAddress(
            connection,
            transactionPda,
            undefined,
            programId
          );

          // 获取提案信息
          let proposalInfo = null;
          try {
            proposalInfo = await multisig.accounts.Proposal.fromAccountAddress(
              connection,
              proposalPda,
              undefined,
              programId
            );
          } catch (error) {
            // 提案可能不存在
          }

          txList.push({
            index: i,
            transaction: txInfo,
            proposal: proposalInfo,
            transactionPda,
            proposalPda,
          });
        } catch (error) {
          // 交易可能不存在，跳过
        }
      }

      setTransactions(txList.reverse()); // 最新的在前面
    } catch (error) {
      console.error('加载交易列表失败:', error);
      toast.error('加载交易列表失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // 投票批准交易
  const approveTransaction = async (transactionIndex) => {
    if (!publicKey) {
      toast.error('请先连接钱包');
      return;
    }

    const multisigPda = getMultisigPda();
    const programId = getProgramId();

    setActionLoading(prev => ({ ...prev, [`approve-${transactionIndex}`]: true }));

    try {
      const transaction = new Transaction();
      
      // 检查是否需要创建提案
      const tx = transactions.find(t => t.index === transactionIndex);
      if (!tx.proposal) {
        const createProposalInstruction = multisig.instructions.proposalCreate({
          multisigPda,
          creator: publicKey,
          isDraft: false,
          transactionIndex: BigInt(transactionIndex),
          rentPayer: publicKey,
          programId,
        });
        transaction.add(createProposalInstruction);
      }

      // 添加投票指令
      const approveInstruction = multisig.instructions.proposalApprove({
        multisigPda,
        member: publicKey,
        transactionIndex: BigInt(transactionIndex),
        programId,
      });
      transaction.add(approveInstruction);

      const signature = await sendTransaction(transaction, connection);
      
      toast.loading('确认交易中...', { id: `approve-${transactionIndex}` });
      await connection.confirmTransaction(signature);
      
      toast.success('投票成功！', { id: `approve-${transactionIndex}` });
      
      // 重新加载交易列表
      await loadTransactions();
    } catch (error) {
      console.error('投票失败:', error);
      toast.error('投票失败: ' + error.message);
    } finally {
      setActionLoading(prev => ({ ...prev, [`approve-${transactionIndex}`]: false }));
    }
  };

  // 执行交易
  const executeTransaction = async (transactionIndex) => {
    if (!publicKey) {
      toast.error('请先连接钱包');
      return;
    }

    const multisigPda = getMultisigPda();
    const programId = getProgramId();

    setActionLoading(prev => ({ ...prev, [`execute-${transactionIndex}`]: true }));

    try {
      const signature = await multisig.rpc.vaultTransactionExecute({
        connection,
        feePayer: { publicKey, secretKey: null }, // 只需要 publicKey 用于签名
        multisigPda,
        transactionIndex: BigInt(transactionIndex),
        member: publicKey,
        programId,
        sendOptions: { skipPreflight: true },
      });

      toast.loading('确认交易中...', { id: `execute-${transactionIndex}` });
      await connection.confirmTransaction(signature);
      
      toast.success('交易执行成功！', { id: `execute-${transactionIndex}` });
      
      // 重新加载交易列表
      await loadTransactions();
    } catch (error) {
      console.error('执行交易失败:', error);
      toast.error('执行交易失败: ' + error.message);
    } finally {
      setActionLoading(prev => ({ ...prev, [`execute-${transactionIndex}`]: false }));
    }
  };

  // 获取交易状态
  const getTransactionStatus = (tx) => {
    if (!tx.proposal) return 'pending';
    if (tx.transaction.executed) return 'executed';
    if (tx.proposal.approved.length >= multisigInfo?.threshold) return 'ready';
    return 'voting';
  };

  // 获取状态显示
  const getStatusDisplay = (status) => {
    switch (status) {
      case 'pending': return { text: '等待提案', color: '#ffc107' };
      case 'voting': return { text: '投票中', color: '#17a2b8' };
      case 'ready': return { text: '可执行', color: '#28a745' };
      case 'executed': return { text: '已执行', color: '#6c757d' };
      default: return { text: '未知', color: '#6c757d' };
    }
  };

  // 检查用户是否已投票
  const hasUserVoted = (tx) => {
    if (!tx.proposal || !publicKey) return false;
    return tx.proposal.approved.some(member => member.equals(publicKey));
  };

  useEffect(() => {
    if (publicKey) {
      loadTransactions();
    }
  }, [publicKey]);

  if (!publicKey) {
    return (
      <div className="status info">
        ℹ️ 请先连接钱包以查看交易列表
      </div>
    );
  }

  return (
    <div>
      <h3>📋 交易列表</h3>
      
      <div style={{ marginBottom: '20px' }}>
        <button className="button" onClick={loadTransactions} disabled={loading}>
          {loading ? '加载中...' : '刷新列表'}
        </button>
      </div>

      {multisigInfo && (
        <div className="status info">
          <p><strong>多签配置:</strong> {multisigInfo.threshold}/{multisigInfo.members.length} 阈值</p>
          <p><strong>当前交易索引:</strong> {Number(multisigInfo.transactionIndex)}</p>
        </div>
      )}

      {loading ? (
        <div className="status info">加载交易列表中...</div>
      ) : transactions.length === 0 ? (
        <div className="status info">暂无交易记录</div>
      ) : (
        <div>
          {transactions.map((tx) => {
            const status = getTransactionStatus(tx);
            const statusDisplay = getStatusDisplay(status);
            const userVoted = hasUserVoted(tx);

            return (
              <div key={tx.index} className="card" style={{ marginBottom: '15px' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <div>
                    <h4>交易 #{tx.index}</h4>
                    <p><strong>创建者:</strong> {tx.transaction.creator.toBase58().slice(0, 8)}...{tx.transaction.creator.toBase58().slice(-8)}</p>
                    <p><strong>备注:</strong> {tx.transaction.memo || '无'}</p>
                    <p>
                      <strong>状态:</strong> 
                      <span style={{ color: statusDisplay.color, fontWeight: 'bold', marginLeft: '5px' }}>
                        {statusDisplay.text}
                      </span>
                    </p>
                    {tx.proposal && (
                      <p><strong>投票进度:</strong> {tx.proposal.approved.length}/{multisigInfo?.threshold}</p>
                    )}
                  </div>
                  
                  <div>
                    {status === 'pending' && (
                      <button
                        className="button"
                        onClick={() => approveTransaction(tx.index)}
                        disabled={actionLoading[`approve-${tx.index}`]}
                      >
                        {actionLoading[`approve-${tx.index}`] ? '处理中...' : '创建提案并投票'}
                      </button>
                    )}
                    
                    {status === 'voting' && !userVoted && (
                      <button
                        className="button"
                        onClick={() => approveTransaction(tx.index)}
                        disabled={actionLoading[`approve-${tx.index}`]}
                      >
                        {actionLoading[`approve-${tx.index}`] ? '处理中...' : '投票批准'}
                      </button>
                    )}
                    
                    {status === 'voting' && userVoted && (
                      <span style={{ color: '#28a745' }}>✅ 已投票</span>
                    )}
                    
                    {status === 'ready' && (
                      <button
                        className="button"
                        onClick={() => executeTransaction(tx.index)}
                        disabled={actionLoading[`execute-${tx.index}`]}
                        style={{ backgroundColor: '#28a745' }}
                      >
                        {actionLoading[`execute-${tx.index}`] ? '执行中...' : '执行交易'}
                      </button>
                    )}
                    
                    {status === 'executed' && (
                      <span style={{ color: '#6c757d' }}>✅ 已执行</span>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}

      <div className="status info">
        <h4>📝 操作说明</h4>
        <ul>
          <li><strong>等待提案:</strong> 交易已创建但未创建投票提案</li>
          <li><strong>投票中:</strong> 提案已创建，等待足够的投票</li>
          <li><strong>可执行:</strong> 已达到投票阈值，可以执行交易</li>
          <li><strong>已执行:</strong> 交易已成功执行</li>
        </ul>
      </div>
    </div>
  );
};

export default TransactionList;
