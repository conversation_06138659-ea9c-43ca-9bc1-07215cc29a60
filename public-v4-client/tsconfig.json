{"compilerOptions": {"target": "ES6", "module": "ESNext", "jsx": "react-jsx", "strict": true, "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "baseUrl": "src", "paths": {"~/*": ["./*"], "@/components/*": ["components/*"], "@/hooks/*": ["hooks/*"], "@/lib/*": ["lib/*"]}}, "include": ["**/*.ts"], "exclude": ["node_modules", "build"]}