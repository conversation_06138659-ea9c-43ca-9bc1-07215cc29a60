// Squads 多签交易状态查询工具
// 使用: node examples/getTransaction-simple.js --txhash=xxx 或 --index=31 或 --latest

import { Connection, PublicKey } from '@solana/web3.js';
import * as multisig from '@sqds/multisig';

const connection = new Connection("https://api.mainnet-beta.solana.com", "confirmed");
const multisigPda = new PublicKey("HVx9YZh7ZNiNzBkRVyoGc3PxsznCJyv3PPz57fEKVuFr");
const programId = new PublicKey("SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf");

async function main() {
  try {
    const args = process.argv.slice(2);
    let transactionIndex = null;

    // 解析参数
    if (args.find(arg => arg.startsWith('--txhash='))) {
      const txhash = args.find(arg => arg.startsWith('--txhash=')).split('=')[1];
      const tx = await connection.getTransaction(txhash, { maxSupportedTransactionVersion: 0 });
      if (tx?.meta?.logMessages) {
        const log = tx.meta.logMessages.find(log => log.includes('index'));
        const match = log?.match(/(\d+)/);
        transactionIndex = match ? BigInt(match[1]) : null;
      }
    } else if (args.find(arg => arg.startsWith('--index='))) {
      transactionIndex = BigInt(args.find(arg => arg.startsWith('--index=')).split('=')[1]);
    } else if (args.includes('--latest')) {
      const multisigInfo = await multisig.accounts.Multisig.fromAccountAddress(connection, multisigPda);
      transactionIndex = multisigInfo.transactionIndex;
    }

    if (!transactionIndex) {
      console.log('请提供参数: --txhash=xxx 或 --index=31 或 --latest');
      return;
    }

    console.log(`🎯 查询交易索引: ${transactionIndex}`);

    // 获取多签信息
    const multisigInfo = await multisig.accounts.Multisig.fromAccountAddress(connection, multisigPda);
    console.log(`📋 多签配置: ${multisigInfo.threshold} 阈值, ${multisigInfo.members.length} 成员`);

    // 获取提案信息
    const [proposalPda] = multisig.getProposalPda({ multisigPda, transactionIndex, programId });
    const proposalInfo = await multisig.accounts.Proposal.fromAccountAddress(connection, proposalPda, undefined, programId);

    console.log(`🗳️  提案状态: ${Object.keys(proposalInfo.status)[0]}`);
    console.log(`👍 已批准: ${proposalInfo.approved.length}/${multisigInfo.threshold}`);

    // 显示已签名成员
    if (proposalInfo.approved.length > 0) {
      console.log('✅ 已签名成员:');
      proposalInfo.approved.forEach((member, i) => console.log(`   ${i + 1}. ${member.toBase58()}`));
    }

    // 显示签名进度
    const allSigned = proposalInfo.approved.length === multisigInfo.members.length;
    console.log(`✍️  全部签名: ${allSigned ? '✅ 是' : '❌ 否'} (${proposalInfo.approved.length}/${multisigInfo.members.length})`);

    if (proposalInfo.rejected.length > 0) {
      console.log(`❌ 已拒绝: ${proposalInfo.rejected.length} 个成员`);
    }

    // 检查执行状态
    const canExecute = proposalInfo.approved.length >= multisigInfo.threshold;
    console.log(`🎯 可执行: ${canExecute ? '✅ 是' : '❌ 否'}`);

    // 检查执行状态（简化版）
    console.log(`🚀 执行状态: ${Object.keys(proposalInfo.status)[0] === 'executed' ? '✅ 已执行' : '⏳ 未执行'}`);
    if (canExecute && Object.keys(proposalInfo.status)[0] !== 'executed') {
      console.log('💡 可以执行此交易');
    }

  } catch (error) {
    console.error('❌ 查询失败:', error.message);
  }
}

main();
