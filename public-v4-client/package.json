{"name": "squads-public-client-v4", "version": "1.2.3", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "webpack serve --config webpack.dev.js", "build": "webpack --config webpack.prod.js"}, "author": "", "license": "ISC", "description": "", "dependencies": {"@radix-ui/react-dialog": "1.1.6", "@radix-ui/react-popover": "1.1.6", "@radix-ui/react-select": "2.0.0", "@radix-ui/react-slot": "1.0.2", "@solana/spl-token": "0.3.11", "@solana/wallet-adapter-base": "0.9.23", "@solana/wallet-adapter-react": "0.15.35", "@solana/wallet-adapter-react-ui": "0.9.35", "@solana/web3.js": "1.95.8", "@sqds/multisig": "2.1.3", "@tailwindcss/vite": "^4.0.9", "@tanstack/react-query": "5.66.9", "bs58": "6.0.0", "buffer": "^6.0.3", "class-variance-authority": "0.7.0", "cmdk": "0.2.0", "events": "^3.3.0", "invariant": "2.2.4", "isbot": "4", "lucide-react": "0.309.0", "process": "0.11.10", "react": "19.0.0", "react-dom": "19.0.0", "react-router-dom": "7.2.0", "sonner": "1.3.1", "tailwind-merge": "2.2.0", "tailwindcss-animate": "1.0.7"}, "devDependencies": {"@commitlint/cli": "19.7.1", "@commitlint/config-conventional": "19.7.1", "@semantic-release/changelog": "6.0.3", "@semantic-release/git": "10.0.1", "@types/invariant": "2.2.37", "@types/node": "22.13.9", "@types/react": "19.0.10", "@types/react-dom": "19.0.4", "autoprefixer": "10.4.20", "copy-webpack-plugin": "13.0.0", "css-loader": "7.1.2", "eslint": "9.21.0", "html-webpack-plugin": "5.6.3", "husky": "9.1.7", "postcss": "8.5.3", "postcss-loader": "8.1.1", "prettier": "3.5.3", "prettier-plugin-tailwindcss": "0.6.11", "style-loader": "4.0.0", "tailwindcss": "3.3.0", "ts-loader": "9.5.2", "tsconfig-paths-webpack-plugin": "4.2.0", "typescript": "5.8.2", "webpack": "5.98.0", "webpack-cli": "6.0.1", "webpack-dev-server": "5.2.0", "webpack-manifest-plugin": "5.0.0"}, "resolutions": {"@babel/runtime": "7.26.10", "@babel/helpers": "7.26.10", "@babel/core": "7.26.10"}}