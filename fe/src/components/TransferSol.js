import React, { useState } from 'react';
import { useWallet, useConnection } from '@solana/wallet-adapter-react';
import { PublicKey, SystemProgram, TransactionMessage, VersionedTransaction, LAMPORTS_PER_SOL } from '@solana/web3.js';
import * as multisig from '@sqds/multisig';
import { useMultisig } from '../hooks/useMultisig';
import toast from 'react-hot-toast';

const TransferSol = () => {
  const { publicKey, sendTransaction } = useWallet();
  const { connection } = useConnection();
  const { getMultisigPda, getProgramId, getVaultPda } = useMultisig();

  const [formData, setFormData] = useState({
    recipient: '',
    amount: '',
    memo: '',
  });
  const [loading, setLoading] = useState(false);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleTransfer = async () => {
    if (!publicKey) {
      toast.error('请先连接钱包');
      return;
    }

    const multisigPda = getMultisigPda();
    const programId = getProgramId();
    const vaultPda = getVaultPda();

    if (!multisigPda || !vaultPda) {
      toast.error('请先在"多签配置"中设置多签地址');
      return;
    }

    if (!formData.recipient || !formData.amount) {
      toast.error('请填写收款地址和转账金额');
      return;
    }

    setLoading(true);

    try {
      // 验证收款地址
      const recipientPubkey = new PublicKey(formData.recipient);
      const lamports = Math.floor(parseFloat(formData.amount) * LAMPORTS_PER_SOL);

      if (lamports <= 0) {
        throw new Error('转账金额必须大于 0');
      }

      // 检查金库余额
      const vaultBalance = await connection.getBalance(vaultPda);
      if (vaultBalance < lamports) {
        throw new Error(`金库余额不足。当前余额: ${(vaultBalance / LAMPORTS_PER_SOL).toFixed(6)} SOL`);
      }

      // 获取多签信息
      const multisigInfo = await multisig.accounts.Multisig.fromAccountAddress(
        connection,
        multisigPda,
        undefined,
        programId
      );

      // 创建转账指令
      const transferInstruction = SystemProgram.transfer({
        fromPubkey: vaultPda,
        toPubkey: recipientPubkey,
        lamports,
      });

      // 创建交易消息
      const { blockhash } = await connection.getLatestBlockhash();
      const transferMessage = new TransactionMessage({
        payerKey: vaultPda,
        recentBlockhash: blockhash,
        instructions: [transferInstruction],
      });

      const transactionIndex = BigInt(multisigInfo.transactionIndex) + 1n;

      // 创建多签交易指令
      const createIx = multisig.instructions.vaultTransactionCreate({
        multisigPda,
        transactionIndex,
        creator: publicKey,
        vaultIndex: 0,
        ephemeralSigners: 0,
        transactionMessage: transferMessage,
        memo: formData.memo || `Transfer ${formData.amount} SOL`,
        programId,
      });

      // 创建提案指令
      const proposalIx = multisig.instructions.proposalCreate({
        multisigPda,
        transactionIndex,
        creator: publicKey,
        isDraft: false,
        programId,
      });

      // 创建投票指令
      const approveIx = multisig.instructions.proposalApprove({
        multisigPda,
        transactionIndex,
        member: publicKey,
        programId,
      });

      // 组合所有指令
      const combinedMessage = new TransactionMessage({
        payerKey: publicKey,
        recentBlockhash: blockhash,
        instructions: [createIx, proposalIx, approveIx],
      });

      const transaction = new VersionedTransaction(combinedMessage.compileToV0Message());

      // 发送交易
      const signature = await sendTransaction(transaction, connection, {
        skipPreflight: true,
      });

      toast.loading('确认交易中...', { id: 'transfer' });
      await connection.confirmTransaction(signature);

      toast.success(`SOL 转账交易已创建并投票！交易索引: ${transactionIndex}`, { id: 'transfer' });

      // 清空表单
      setFormData({ recipient: '', amount: '', memo: '' });

    } catch (error) {
      console.error('转账失败:', error);
      toast.error('转账失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <h3>💰 SOL 转账</h3>

      <div className="form-group">
        <label className="label">收款地址</label>
        <input
          type="text"
          name="recipient"
          value={formData.recipient}
          onChange={handleInputChange}
          className="input"
          placeholder="输入收款地址..."
          disabled={loading}
        />
      </div>

      <div className="form-group">
        <label className="label">转账金额 (SOL)</label>
        <input
          type="number"
          name="amount"
          value={formData.amount}
          onChange={handleInputChange}
          className="input"
          placeholder="0.001"
          step="0.001"
          min="0"
          disabled={loading}
        />
      </div>

      <div className="form-group">
        <label className="label">备注 (可选)</label>
        <input
          type="text"
          name="memo"
          value={formData.memo}
          onChange={handleInputChange}
          className="input"
          placeholder="转账备注..."
          disabled={loading}
        />
      </div>

      <button
        className="button"
        onClick={handleTransfer}
        disabled={!publicKey || loading}
      >
        {loading ? '处理中...' : '创建 SOL 转账'}
      </button>

      <div className="status info">
        <h4>📝 操作说明</h4>
        <ul>
          <li>输入有效的 Solana 地址作为收款地址</li>
          <li>输入转账金额（以 SOL 为单位）</li>
          <li>点击"创建 SOL 转账"将创建交易并自动投票</li>
          <li>如果多签需要更多投票，请在"交易列表"中继续投票</li>
          <li>达到投票阈值后可以执行交易</li>
        </ul>
      </div>
    </div>
  );
};

export default TransferSol;
