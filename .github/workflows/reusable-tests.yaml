name: Reusable Tests

on:
  workflow_call:
    inputs:
      cache:
        required: true
        type: boolean
      solana_cli_version:
        required: true
        type: string
      node_version:
        required: true
        type: string
      cargo_profile:
        required: true
        type: string
      anchor_version:
        required: true
        type: string

env:
  CACHE: inputs.cache
  SOLANA_CLI_VERSION: ${{ inputs.solana_cli_version }}
  NODE_VERSION: ${{ inputs.node_version }}
  CARGO_PROFILE: ${{ inputs.cargo_profile }}
  ANCHOR_VERSION: ${{ inputs.anchor_version }}

jobs:
  tests:
    name: Tests
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup Solana
        uses: ./.github/actions/setup-solana/

      - name: Setup Rust
        uses: ./.github/actions/setup-rust/

      - name: Setup Anchor
        uses: ./.github/actions/setup-anchor/

      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: yarn

      - name: Yarn Install
        run: yarn install --immutable

      - name: Cache Solita Build Dependencies
        uses: actions/cache@v3
        id: cache-solita-build-dependencies
        with:
          path: |
            .crates
          key: solita-crates-${{ runner.os }}-${{ hashFiles('./sdk/multisig/.solitarc.js') }}

      - name: Cache Cargo `target` directory
        uses: actions/cache@v3
        id: cache-cargo-target
        with:
          path: ./target/
          key: target-dir-${{ runner.os }}-${{ env.CARGO_PROFILE }}-${{ hashFiles('./Cargo.lock') }}

      - name: Build Program
        run: yarn build

      - name: Run Tests
        run: yarn test
